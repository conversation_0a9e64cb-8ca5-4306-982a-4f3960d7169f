const fs = require('fs');
const path = require('path');

// 需要修复的文件列表
const files = [
  'src/controllers/adminProductController.js',
  'src/controllers/adminInventoryController.js',
  'src/controllers/adminOrderController.js',
  'src/controllers/adminDashboardController.js',
  'src/controllers/adminSettingsController.js'
];

// 修复函数
function fixSuccessCalls(content) {
  // 匹配 success(res, 'message', { data }) 模式并替换为 success(res, { data }, 'message')
  return content.replace(
    /success\(res,\s*'([^']+)',\s*(\{[^}]*\}(?:\s*,\s*\{[^}]*\})*)\s*\)/g,
    "success(res, $2, '$1')"
  ).replace(
    /success\(res,\s*'([^']+)',\s*(\{[\s\S]*?\})\s*,\s*(\d+)\s*\)/g,
    "success(res, $2, '$1', $3)"
  ).replace(
    /success\(res,\s*`([^`]+)`,\s*(\{[^}]*\})\s*\)/g,
    "success(res, $2, `$1`)"
  );
}

// 处理每个文件
files.forEach(filePath => {
  if (fs.existsSync(filePath)) {
    console.log(`Processing ${filePath}...`);
    const content = fs.readFileSync(filePath, 'utf8');
    const fixedContent = fixSuccessCalls(content);
    
    if (content !== fixedContent) {
      fs.writeFileSync(filePath, fixedContent);
      console.log(`Fixed ${filePath}`);
    } else {
      console.log(`No changes needed for ${filePath}`);
    }
  } else {
    console.log(`File not found: ${filePath}`);
  }
});

console.log('Done!');
