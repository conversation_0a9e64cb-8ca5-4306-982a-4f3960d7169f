const db = require('../config/database');
const { success, error, notFound } = require('../utils/response');

/**
 * 获取库存列表
 */
async function getInventoryList(req, res) {
  try {
    const {
      page = 1,
      limit = 20,
      product_id = '',
      status = 'all',
      search = '',
      sort = 'created_desc'
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);
    
    // 构建查询条件
    let whereClause = 'WHERE 1=1';
    let queryParams = [];

    // 商品筛选
    if (product_id) {
      whereClause += ' AND pi.product_id = ?';
      queryParams.push(product_id);
    }

    // 状态筛选
    if (status !== 'all') {
      whereClause += ' AND pi.status = ?';
      queryParams.push(status);
    }

    // 搜索条件（邮箱账号）
    if (search) {
      whereClause += ' AND pi.email_account LIKE ?';
      queryParams.push(`%${search}%`);
    }

    // 排序条件
    let orderClause = 'ORDER BY ';
    switch (sort) {
      case 'created_asc':
        orderClause += 'pi.created_at ASC';
        break;
      case 'created_desc':
        orderClause += 'pi.created_at DESC';
        break;
      case 'updated_asc':
        orderClause += 'pi.updated_at ASC';
        break;
      case 'updated_desc':
        orderClause += 'pi.updated_at DESC';
        break;
      case 'product_name':
        orderClause += 'p.name ASC';
        break;
      default:
        orderClause += 'pi.created_at DESC';
    }

    // 查询库存列表
    const [inventory] = await db.execute(
      `SELECT 
         pi.id,
         pi.product_id,
         pi.email_account,
         pi.email_password,
         pi.status,
         pi.created_at,
         pi.updated_at,
         p.name as product_name,
         p.price as product_price,
         oi.order_id,
         o.order_number
       FROM product_inventory pi
       LEFT JOIN products p ON pi.product_id = p.id
       LEFT JOIN order_items oi ON pi.id = oi.inventory_id
       LEFT JOIN orders o ON oi.order_id = o.id
       ${whereClause}
       ${orderClause}
       LIMIT ? OFFSET ?`,
      [...queryParams, parseInt(limit), offset]
    );

    // 查询总数
    const [countResult] = await db.execute(
      `SELECT COUNT(*) as total 
       FROM product_inventory pi
       LEFT JOIN products p ON pi.product_id = p.id
       ${whereClause}`,
      queryParams
    );

    const total = countResult[0].total;
    const totalPages = Math.ceil(total / parseInt(limit));

    // 格式化库存数据
    const formattedInventory = inventory.map(item => ({
      id: item.id,
      product_id: item.product_id,
      product_name: item.product_name,
      product_price: parseFloat(item.product_price || 0),
      email_account: item.email_account,
      email_password: item.email_password,
      status: item.status,
      order_info: item.order_id ? {
        order_id: item.order_id,
        order_number: item.order_number
      } : null,
      created_at: item.created_at,
      updated_at: item.updated_at
    }));

    success(res, {
      inventory: formattedInventory,
      pagination: {
        current_page: parseInt(page),
        per_page: parseInt(limit),
        total: total,
        total_pages: totalPages,
        has_next: parseInt(page) < totalPages,
        has_prev: parseInt(page) > 1
      }
    });

  } catch (err) {
    console.error('获取库存列表错误:', err);
    error(res, '获取库存列表失败', 500);
  }
}

/**
 * 获取库存详情
 */
async function getInventoryDetail(req, res) {
  try {
    const { id } = req.params;

    // 查询库存详情
    const [inventory] = await db.execute(
      `SELECT 
         pi.*,
         p.name as product_name,
         p.price as product_price,
         p.description as product_description,
         oi.order_id,
         o.order_number,
         o.user_id,
         u.username,
         u.email as user_email
       FROM product_inventory pi
       LEFT JOIN products p ON pi.product_id = p.id
       LEFT JOIN order_items oi ON pi.id = oi.inventory_id
       LEFT JOIN orders o ON oi.order_id = o.id
       LEFT JOIN users u ON o.user_id = u.id
       WHERE pi.id = ?`,
      [id]
    );

    if (inventory.length === 0) {
      return notFound(res, '库存记录不存在');
    }

    const item = inventory[0];

    const inventoryDetail = {
      id: item.id,
      product_id: item.product_id,
      product_info: {
        name: item.product_name,
        price: parseFloat(item.product_price || 0),
        description: item.product_description
      },
      email_account: item.email_account,
      email_password: item.email_password,
      status: item.status,
      order_info: item.order_id ? {
        order_id: item.order_id,
        order_number: item.order_number,
        user_id: item.user_id,
        username: item.username,
        user_email: item.user_email
      } : null,
      created_at: item.created_at,
      updated_at: item.updated_at
    };

    success(res, { inventory: inventoryDetail }, '获取库存详情成功');

  } catch (err) {
    console.error('获取库存详情错误:', err);
    error(res, '获取库存详情失败', 500);
  }
}

/**
 * 添加单个库存
 */
async function createInventory(req, res) {
  try {
    const { product_id, email_account, email_password, status = 'available' } = req.body;

    // 基础验证
    if (!product_id || !email_account || !email_password) {
      return error(res, '商品ID、邮箱账号和密码不能为空', 400);
    }

    if (!['available', 'reserved'].includes(status)) {
      return error(res, '库存状态无效', 400);
    }

    // 检查商品是否存在
    const [products] = await db.execute(
      'SELECT id FROM products WHERE id = ?',
      [product_id]
    );

    if (products.length === 0) {
      return error(res, '商品不存在', 400);
    }

    // 检查邮箱账号是否已存在
    const [existingInventory] = await db.execute(
      'SELECT id FROM product_inventory WHERE email_account = ?',
      [email_account]
    );

    if (existingInventory.length > 0) {
      return error(res, '该邮箱账号已存在', 400);
    }

    // 创建库存记录
    const [result] = await db.execute(
      'INSERT INTO product_inventory (product_id, email_account, email_password, status) VALUES (?, ?, ?, ?)',
      [product_id, email_account, email_password, status]
    );

    // 获取创建的库存信息
    const [newInventory] = await db.execute(
      `SELECT 
         pi.*,
         p.name as product_name
       FROM product_inventory pi
       LEFT JOIN products p ON pi.product_id = p.id
       WHERE pi.id = ?`,
      [result.insertId]
    );

    const formattedInventory = {
      ...newInventory[0],
      product_name: newInventory[0].product_name
    };

    success(res, '库存添加成功', { inventory: formattedInventory }, '获取库存列表成功', 201);

  } catch (err) {
    console.error('添加库存错误:', err);
    error(res, '添加库存失败', 500);
  }
}

/**
 * 批量导入库存
 */
async function batchImportInventory(req, res) {
  try {
    const { product_id, inventory_list } = req.body;

    // 基础验证
    if (!product_id || !Array.isArray(inventory_list) || inventory_list.length === 0) {
      return error(res, '请提供商品ID和库存列表', 400);
    }

    if (inventory_list.length > 1000) {
      return error(res, '单次导入库存数量不能超过1000条', 400);
    }

    // 检查商品是否存在
    const [products] = await db.execute(
      'SELECT id FROM products WHERE id = ?',
      [product_id]
    );

    if (products.length === 0) {
      return error(res, '商品不存在', 400);
    }

    // 验证库存数据格式
    const validInventory = [];
    const errors = [];

    for (let i = 0; i < inventory_list.length; i++) {
      const item = inventory_list[i];
      
      if (!item.email_account || !item.email_password) {
        errors.push(`第${i + 1}行：邮箱账号和密码不能为空`);
        continue;
      }

      // 检查邮箱格式
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(item.email_account)) {
        errors.push(`第${i + 1}行：邮箱格式无效`);
        continue;
      }

      validInventory.push({
        email_account: item.email_account.trim(),
        email_password: item.email_password.trim(),
        status: item.status || 'available'
      });
    }

    if (errors.length > 0) {
      return error(res, '数据验证失败', 400, { errors });
    }

    // 检查重复的邮箱账号
    const emailAccounts = validInventory.map(item => item.email_account);
    const [existingInventory] = await db.execute(
      `SELECT email_account FROM product_inventory WHERE email_account IN (${emailAccounts.map(() => '?').join(',')})`,
      emailAccounts
    );

    if (existingInventory.length > 0) {
      const duplicateEmails = existingInventory.map(item => item.email_account);
      return error(res, '以下邮箱账号已存在', 400, { duplicate_emails: duplicateEmails });
    }

    // 开始批量插入
    await db.execute('START TRANSACTION');

    try {
      let successCount = 0;
      
      for (const item of validInventory) {
        await db.execute(
          'INSERT INTO product_inventory (product_id, email_account, email_password, status) VALUES (?, ?, ?, ?)',
          [product_id, item.email_account, item.email_password, item.status]
        );
        successCount++;
      }

      await db.execute('COMMIT');

      success(res, {
        imported_count: successCount,
        total_count: inventory_list.length
      }, `成功导入${successCount}条库存记录`);

    } catch (err) {
      await db.execute('ROLLBACK');
      throw err;
    }

  } catch (err) {
    console.error('批量导入库存错误:', err);
    error(res, '批量导入库存失败', 500);
  }
}

/**
 * 更新库存
 */
async function updateInventory(req, res) {
  try {
    const { id } = req.params;
    const { email_account, email_password, status } = req.body;

    // 检查库存是否存在
    const [inventory] = await db.execute(
      'SELECT id, status FROM product_inventory WHERE id = ?',
      [id]
    );

    if (inventory.length === 0) {
      return notFound(res, '库存记录不存在');
    }

    // 如果库存已售出，不允许修改
    if (inventory[0].status === 'sold') {
      return error(res, '已售出的库存不能修改', 400);
    }

    // 构建更新字段
    let updateFields = [];
    let updateValues = [];

    if (email_account !== undefined) {
      // 检查邮箱账号是否已被其他库存使用
      const [existingInventory] = await db.execute(
        'SELECT id FROM product_inventory WHERE email_account = ? AND id != ?',
        [email_account, id]
      );
      if (existingInventory.length > 0) {
        return error(res, '该邮箱账号已被使用', 400);
      }
      updateFields.push('email_account = ?');
      updateValues.push(email_account);
    }

    if (email_password !== undefined) {
      updateFields.push('email_password = ?');
      updateValues.push(email_password);
    }

    if (status !== undefined) {
      if (!['available', 'sold', 'reserved'].includes(status)) {
        return error(res, '库存状态无效', 400);
      }
      updateFields.push('status = ?');
      updateValues.push(status);
    }

    if (updateFields.length === 0) {
      return error(res, '没有提供要更新的信息', 400);
    }

    // 执行更新
    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    updateValues.push(id);

    await db.execute(
      `UPDATE product_inventory SET ${updateFields.join(', ')} WHERE id = ?`,
      updateValues
    );

    // 获取更新后的库存信息
    const [updatedInventory] = await db.execute(
      `SELECT 
         pi.*,
         p.name as product_name
       FROM product_inventory pi
       LEFT JOIN products p ON pi.product_id = p.id
       WHERE pi.id = ?`,
      [id]
    );

    success(res, { inventory: updatedInventory[0] }, '库存更新成功');

  } catch (err) {
    console.error('更新库存错误:', err);
    error(res, '更新库存失败', 500);
  }
}

/**
 * 删除库存
 */
async function deleteInventory(req, res) {
  try {
    const { id } = req.params;

    // 检查库存是否存在
    const [inventory] = await db.execute(
      'SELECT id, status FROM product_inventory WHERE id = ?',
      [id]
    );

    if (inventory.length === 0) {
      return notFound(res, '库存记录不存在');
    }

    // 如果库存已售出，不允许删除
    if (inventory[0].status === 'sold') {
      return error(res, '已售出的库存不能删除', 400);
    }

    // 删除库存
    await db.execute('DELETE FROM product_inventory WHERE id = ?', [id]);

    success(res, '库存删除成功', null);

  } catch (err) {
    console.error('删除库存错误:', err);
    error(res, '删除库存失败', 500);
  }
}

/**
 * 批量更新库存状态
 */
async function batchUpdateStatus(req, res) {
  try {
    const { inventory_ids, status } = req.body;

    if (!Array.isArray(inventory_ids) || inventory_ids.length === 0) {
      return error(res, '请提供要更新的库存ID列表', 400);
    }

    if (!['available', 'reserved'].includes(status)) {
      return error(res, '库存状态无效（不能批量设置为已售出）', 400);
    }

    // 检查是否有已售出的库存
    const placeholders = inventory_ids.map(() => '?').join(',');
    const [soldInventory] = await db.execute(
      `SELECT COUNT(*) as count FROM product_inventory WHERE id IN (${placeholders}) AND status = 'sold'`,
      inventory_ids
    );

    if (soldInventory[0].count > 0) {
      return error(res, '选择的库存中包含已售出的记录，无法批量更新', 400);
    }

    // 批量更新状态
    const [result] = await db.execute(
      `UPDATE product_inventory SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id IN (${placeholders})`,
      [status, ...inventory_ids]
    );

    success(res, {
      updated_count: result.affectedRows,
      status: status
    }, `成功更新${result.affectedRows}条库存记录的状态`);

  } catch (err) {
    console.error('批量更新库存状态错误:', err);
    error(res, '批量更新库存状态失败', 500);
  }
}

/**
 * 获取库存统计
 */
async function getInventoryStats(req, res) {
  try {
    const { product_id } = req.query;

    let whereClause = '';
    let queryParams = [];

    if (product_id) {
      whereClause = 'WHERE pi.product_id = ?';
      queryParams.push(product_id);
    }

    // 查询库存统计
    const [stats] = await db.execute(
      `SELECT 
         COUNT(*) as total_count,
         COUNT(CASE WHEN pi.status = 'available' THEN 1 END) as available_count,
         COUNT(CASE WHEN pi.status = 'sold' THEN 1 END) as sold_count,
         COUNT(CASE WHEN pi.status = 'reserved' THEN 1 END) as reserved_count,
         COUNT(DISTINCT pi.product_id) as product_count
       FROM product_inventory pi
       ${whereClause}`,
      queryParams
    );

    // 查询各商品库存分布
    const [productStats] = await db.execute(
      `SELECT 
         p.id,
         p.name,
         COUNT(pi.id) as total_inventory,
         COUNT(CASE WHEN pi.status = 'available' THEN 1 END) as available_count,
         COUNT(CASE WHEN pi.status = 'sold' THEN 1 END) as sold_count,
         COUNT(CASE WHEN pi.status = 'reserved' THEN 1 END) as reserved_count
       FROM products p
       LEFT JOIN product_inventory pi ON p.id = pi.product_id
       ${product_id ? 'WHERE p.id = ?' : ''}
       GROUP BY p.id, p.name
       ORDER BY available_count ASC`,
      product_id ? [product_id] : []
    );

    const inventoryStats = {
      overview: {
        total_inventory: stats[0].total_count,
        available: stats[0].available_count,
        sold: stats[0].sold_count,
        reserved: stats[0].reserved_count,
        product_count: stats[0].product_count
      },
      by_product: productStats.map(item => ({
        product_id: item.id,
        product_name: item.name,
        total_inventory: item.total_inventory,
        available: item.available_count,
        sold: item.sold_count,
        reserved: item.reserved_count
      }))
    };

    success(res, '获取库存统计成功', inventoryStats);

  } catch (err) {
    console.error('获取库存统计错误:', err);
    error(res, '获取库存统计失败', 500);
  }
}

module.exports = {
  getInventoryList,
  getInventoryDetail,
  createInventory,
  batchImportInventory,
  updateInventory,
  deleteInventory,
  batchUpdateStatus,
  getInventoryStats
};
