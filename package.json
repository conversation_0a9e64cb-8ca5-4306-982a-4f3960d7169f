{"name": "edu-shop-backend", "version": "1.0.0", "description": "EDU邮箱自动售卖平台后端API", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "keywords": ["edu", "email", "shop", "api"], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "mysql2": "^3.6.5"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.4"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/app.js"], "testMatch": ["**/tests/**/*.test.js"]}}