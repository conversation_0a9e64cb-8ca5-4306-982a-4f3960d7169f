const db = require('../config/database');
const { success, error } = require('../utils/response');

/**
 * 获取商品列表（管理员视图）
 */
async function getProductList(req, res) {
  try {
    const {
      page = 1,
      limit = 20,
      search = '',
      status = 'all',
      sort = 'created_desc'
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);
    
    // 构建查询条件
    let whereClause = 'WHERE 1=1';
    let queryParams = [];

    // 搜索条件
    if (search) {
      whereClause += ' AND name LIKE ?';
      queryParams.push(`%${search}%`);
    }

    // 状态筛选
    if (status !== 'all') {
      whereClause += ' AND status = ?';
      queryParams.push(status);
    }

    // 排序条件
    let orderClause = 'ORDER BY ';
    switch (sort) {
      case 'created_asc':
        orderClause += 'created_at ASC';
        break;
      case 'created_desc':
        orderClause += 'created_at DESC';
        break;
      case 'price_asc':
        orderClause += 'price ASC';
        break;
      case 'price_desc':
        orderClause += 'price DESC';
        break;
      case 'name_asc':
        orderClause += 'name ASC';
        break;
      case 'name_desc':
        orderClause += 'name DESC';
        break;
      default:
        orderClause += 'created_at DESC';
    }

    // 查询商品列表
    const [products] = await db.execute(
      `SELECT 
         p.*,
         COUNT(CASE WHEN pi.status = 'available' THEN 1 END) as available_count,
         COUNT(CASE WHEN pi.status = 'sold' THEN 1 END) as sold_count,
         COUNT(CASE WHEN pi.status = 'reserved' THEN 1 END) as reserved_count,
         COUNT(pi.id) as total_inventory,
         (SELECT COUNT(*) FROM order_items oi 
          JOIN orders o ON oi.order_id = o.id 
          WHERE oi.product_id = p.id AND o.status = 'completed') as sales_count
       FROM products p
       LEFT JOIN product_inventory pi ON p.id = pi.product_id
       ${whereClause}
       GROUP BY p.id
       ${orderClause}
       LIMIT ? OFFSET ?`,
      [...queryParams, parseInt(limit), offset]
    );

    // 查询总数
    const [countResult] = await db.execute(
      `SELECT COUNT(*) as total FROM products ${whereClause}`,
      queryParams
    );

    const total = countResult[0].total;
    const totalPages = Math.ceil(total / parseInt(limit));

    // 格式化商品数据
    const formattedProducts = products.map(product => ({
      id: product.id,
      name: product.name,
      school_logo_url: product.school_logo_url,
      description: product.description,
      price: parseFloat(product.price),
      warranty_days: product.warranty_days,
      status: product.status,
      inventory: {
        available: product.available_count,
        sold: product.sold_count,
        reserved: product.reserved_count,
        total: product.total_inventory
      },
      sales_count: product.sales_count,
      created_at: product.created_at,
      updated_at: product.updated_at
    }));

    success(res, {
      products: formattedProducts,
      pagination: {
        current_page: parseInt(page),
        per_page: parseInt(limit),
        total: total,
        total_pages: totalPages,
        has_next: parseInt(page) < totalPages,
        has_prev: parseInt(page) > 1
      }
    }, '获取商品列表成功');

  } catch (err) {
    console.error('获取商品列表错误:', err);
    error(res, '获取商品列表失败', 500);
  }
}

/**
 * 获取商品详情（管理员视图）
 */
async function getProductDetail(req, res) {
  try {
    const { id } = req.params;

    // 查询商品基本信息
    const [products] = await db.execute(
      'SELECT * FROM products WHERE id = ?',
      [id]
    );

    if (products.length === 0) {
      return error(res, '商品不存在', 404);
    }

    const product = products[0];

    // 查询库存统计
    const [inventoryStats] = await db.execute(
      `SELECT 
         COUNT(CASE WHEN status = 'available' THEN 1 END) as available_count,
         COUNT(CASE WHEN status = 'sold' THEN 1 END) as sold_count,
         COUNT(CASE WHEN status = 'reserved' THEN 1 END) as reserved_count,
         COUNT(*) as total_count
       FROM product_inventory 
       WHERE product_id = ?`,
      [id]
    );

    // 查询销售统计
    const [salesStats] = await db.execute(
      `SELECT 
         COUNT(oi.id) as total_sales,
         COALESCE(SUM(oi.price * oi.quantity), 0) as total_revenue
       FROM order_items oi
       JOIN orders o ON oi.order_id = o.id
       WHERE oi.product_id = ? AND o.status = 'completed'`,
      [id]
    );

    // 查询最近的库存记录
    const [recentInventory] = await db.execute(
      `SELECT id, email_account, status, created_at, updated_at
       FROM product_inventory 
       WHERE product_id = ? 
       ORDER BY created_at DESC 
       LIMIT 10`,
      [id]
    );

    const productDetail = {
      ...product,
      price: parseFloat(product.price),
      inventory: {
        available: inventoryStats[0].available_count,
        sold: inventoryStats[0].sold_count,
        reserved: inventoryStats[0].reserved_count,
        total: inventoryStats[0].total_count
      },
      sales: {
        total_sales: salesStats[0].total_sales,
        total_revenue: parseFloat(salesStats[0].total_revenue)
      },
      recent_inventory: recentInventory.map(item => ({
        id: item.id,
        email_account: item.email_account,
        status: item.status,
        created_at: item.created_at,
        updated_at: item.updated_at
      }))
    };

    success(res, { product: productDetail }, '获取商品详情成功');

  } catch (err) {
    console.error('获取商品详情错误:', err);
    error(res, '获取商品详情失败', 500);
  }
}

/**
 * 创建商品
 */
async function createProduct(req, res) {
  try {
    const {
      name,
      school_logo_url = '',
      description = '',
      price,
      warranty_days = 0,
      status = 'active'
    } = req.body;

    // 基础验证
    if (!name || !price) {
      return error(res, '商品名称和价格不能为空', 400);
    }

    if (parseFloat(price) <= 0) {
      return error(res, '商品价格必须大于0', 400);
    }

    if (!['active', 'inactive'].includes(status)) {
      return error(res, '商品状态无效', 400);
    }

    // 检查商品名称是否已存在
    const [existingProducts] = await db.execute(
      'SELECT id FROM products WHERE name = ?',
      [name]
    );

    if (existingProducts.length > 0) {
      return error(res, '商品名称已存在', 400);
    }

    // 创建商品
    const [result] = await db.execute(
      `INSERT INTO products (name, school_logo_url, description, price, warranty_days, status) 
       VALUES (?, ?, ?, ?, ?, ?)`,
      [name, school_logo_url, description, parseFloat(price), parseInt(warranty_days), status]
    );

    // 获取创建的商品信息
    const [newProduct] = await db.execute(
      'SELECT * FROM products WHERE id = ?',
      [result.insertId]
    );

    const formattedProduct = {
      ...newProduct[0],
      price: parseFloat(newProduct[0].price)
    };

    success(res, '商品创建成功', { product: formattedProduct }, '获取商品列表成功', 201);

  } catch (err) {
    console.error('创建商品错误:', err);
    error(res, '创建商品失败', 500);
  }
}

/**
 * 更新商品
 */
async function updateProduct(req, res) {
  try {
    const { id } = req.params;
    const {
      name,
      school_logo_url,
      description,
      price,
      warranty_days,
      status
    } = req.body;

    // 检查商品是否存在
    const [products] = await db.execute(
      'SELECT id FROM products WHERE id = ?',
      [id]
    );

    if (products.length === 0) {
      return error(res, '商品不存在', 404);
    }

    // 构建更新字段
    let updateFields = [];
    let updateValues = [];

    if (name !== undefined) {
      // 检查商品名称是否已被其他商品使用
      const [existingProducts] = await db.execute(
        'SELECT id FROM products WHERE name = ? AND id != ?',
        [name, id]
      );
      if (existingProducts.length > 0) {
        return error(res, '商品名称已被使用', 400);
      }
      updateFields.push('name = ?');
      updateValues.push(name);
    }

    if (school_logo_url !== undefined) {
      updateFields.push('school_logo_url = ?');
      updateValues.push(school_logo_url);
    }

    if (description !== undefined) {
      updateFields.push('description = ?');
      updateValues.push(description);
    }

    if (price !== undefined) {
      if (parseFloat(price) <= 0) {
        return error(res, '商品价格必须大于0', 400);
      }
      updateFields.push('price = ?');
      updateValues.push(parseFloat(price));
    }

    if (warranty_days !== undefined) {
      updateFields.push('warranty_days = ?');
      updateValues.push(parseInt(warranty_days));
    }

    if (status !== undefined) {
      if (!['active', 'inactive'].includes(status)) {
        return error(res, '商品状态无效', 400);
      }
      updateFields.push('status = ?');
      updateValues.push(status);
    }

    if (updateFields.length === 0) {
      return error(res, '没有提供要更新的信息', 400);
    }

    // 执行更新
    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    updateValues.push(id);

    await db.execute(
      `UPDATE products SET ${updateFields.join(', ')} WHERE id = ?`,
      updateValues
    );

    // 获取更新后的商品信息
    const [updatedProduct] = await db.execute(
      'SELECT * FROM products WHERE id = ?',
      [id]
    );

    const formattedProduct = {
      ...updatedProduct[0],
      price: parseFloat(updatedProduct[0].price)
    };

    success(res, { product: formattedProduct }, '商品更新成功');

  } catch (err) {
    console.error('更新商品错误:', err);
    error(res, '更新商品失败', 500);
  }
}

/**
 * 删除商品
 */
async function deleteProduct(req, res) {
  try {
    const { id } = req.params;

    // 检查商品是否存在
    const [products] = await db.execute(
      'SELECT id FROM products WHERE id = ?',
      [id]
    );

    if (products.length === 0) {
      return error(res, '商品不存在', 404);
    }

    // 检查是否有相关的订单
    const [relatedOrders] = await db.execute(
      'SELECT COUNT(*) as count FROM order_items WHERE product_id = ?',
      [id]
    );

    if (relatedOrders[0].count > 0) {
      return error(res, '该商品有相关订单，无法删除', 400);
    }

    // 开始事务删除商品相关数据
    await db.execute('START TRANSACTION');

    try {
      // 删除库存
      await db.execute('DELETE FROM product_inventory WHERE product_id = ?', [id]);
      
      // 删除购物车中的商品
      await db.execute('DELETE FROM shopping_cart_items WHERE product_id = ?', [id]);
      
      // 删除商品
      await db.execute('DELETE FROM products WHERE id = ?', [id]);

      await db.execute('COMMIT');

      success(res, '商品删除成功', null);

    } catch (err) {
      await db.execute('ROLLBACK');
      throw err;
    }

  } catch (err) {
    console.error('删除商品错误:', err);
    error(res, '删除商品失败', 500);
  }
}

/**
 * 批量更新商品状态
 */
async function batchUpdateStatus(req, res) {
  try {
    const { product_ids, status } = req.body;

    if (!Array.isArray(product_ids) || product_ids.length === 0) {
      return error(res, '请提供要更新的商品ID列表', 400);
    }

    if (!['active', 'inactive'].includes(status)) {
      return error(res, '商品状态无效', 400);
    }

    // 构建占位符
    const placeholders = product_ids.map(() => '?').join(',');
    
    // 批量更新状态
    const [result] = await db.execute(
      `UPDATE products SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id IN (${placeholders})`,
      [status, ...product_ids]
    );

    success(res, {
      updated_count: result.affectedRows,
      status: status
    }, `成功更新${result.affectedRows}个商品的状态`);

  } catch (err) {
    console.error('批量更新商品状态错误:', err);
    error(res, '批量更新商品状态失败', 500);
  }
}

module.exports = {
  getProductList,
  getProductDetail,
  createProduct,
  updateProduct,
  deleteProduct,
  batchUpdateStatus
};
