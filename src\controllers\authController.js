const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { pool } = require('../config/database');
const { validateRegister, validateLogin } = require('../utils/validation');
const { success, error, serverError } = require('../utils/response');

/**
 * 用户注册
 */
async function register(req, res) {
  try {
    // 数据验证
    const { error: validationError, value } = validateRegister(req.body);
    if (validationError) {
      const errorMessage = validationError.details.map(detail => detail.message).join(', ');
      return error(res, errorMessage, 400);
    }

    const { username, email, password } = value;

    // 检查用户名是否已存在
    const [existingUsers] = await pool.execute(
      'SELECT id FROM users WHERE username = ? OR email = ?',
      [username, email]
    );

    if (existingUsers.length > 0) {
      return error(res, '用户名或邮箱已存在', 400);
    }

    // 密码加密
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // 插入新用户
    const [result] = await pool.execute(
      'INSERT INTO users (username, email, password_hash, role) VALUES (?, ?, ?, ?)',
      [username, email, passwordHash, 'customer']
    );

    // 生成JWT token
    const token = jwt.sign(
      { 
        userId: result.insertId, 
        username, 
        email,
        role: 'customer'
      },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN }
    );

    return success(res, {
      user: {
        id: result.insertId,
        username,
        email,
        role: 'customer'
      },
      token
    }, '注册成功', 201);

  } catch (err) {
    console.error('注册错误:', err);
    return serverError(res, '注册失败，请稍后重试');
  }
}

/**
 * 用户登录
 */
async function login(req, res) {
  try {
    // 数据验证
    const { error: validationError, value } = validateLogin(req.body);
    if (validationError) {
      const errorMessage = validationError.details.map(detail => detail.message).join(', ');
      return error(res, errorMessage, 400);
    }

    const { username, password } = value;

    // 查找用户（支持用户名或邮箱登录）
    const [users] = await pool.execute(
      'SELECT id, username, email, password_hash, role FROM users WHERE username = ? OR email = ?',
      [username, username]
    );

    if (users.length === 0) {
      return error(res, '用户名或密码错误', 401);
    }

    const user = users[0];

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password_hash);
    if (!isPasswordValid) {
      return error(res, '用户名或密码错误', 401);
    }

    // 生成JWT token
    const token = jwt.sign(
      { 
        userId: user.id, 
        username: user.username, 
        email: user.email,
        role: user.role
      },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN }
    );

    return success(res, {
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role
      },
      token
    }, '登录成功');

  } catch (err) {
    console.error('登录错误:', err);
    return serverError(res, '登录失败，请稍后重试');
  }
}

/**
 * 刷新JWT token
 */
async function refreshToken(req, res) {
  try {
    const userId = req.user.userId;

    // 从数据库获取最新用户信息
    const [users] = await pool.execute(
      'SELECT id, username, email, role FROM users WHERE id = ?',
      [userId]
    );

    if (users.length === 0) {
      return error(res, '用户不存在', 404);
    }

    const user = users[0];

    // 生成新的JWT token
    const token = jwt.sign(
      {
        userId: user.id,
        username: user.username,
        email: user.email,
        role: user.role
      },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN }
    );

    return success(res, {
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role
      },
      token
    }, 'Token刷新成功');

  } catch (err) {
    console.error('刷新Token错误:', err);
    return serverError(res, '刷新Token失败');
  }
}

module.exports = {
  register,
  login,
  refreshToken
};
