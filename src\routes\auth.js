const express = require('express');
const { register, login, refreshToken } = require('../controllers/authController');
const { authenticateToken } = require('../middleware/auth');
const { success, error, serverError } = require('../utils/response');
const { pool } = require('../config/database');

const router = express.Router();

// 用户注册
router.post('/register', register);

// 用户登录
router.post('/login', login);

// 验证token并返回最新用户信息
router.get('/verify', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;

    // 从数据库获取最新用户信息，而不是使用JWT中的旧数据
    const [users] = await pool.execute(
      'SELECT id, username, email, role, created_at, updated_at FROM users WHERE id = ?',
      [userId]
    );

    if (users.length === 0) {
      return error(res, '用户不存在', 404);
    }

    const user = users[0];

    return success(res, {
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        created_at: user.created_at,
        updated_at: user.updated_at
      }
    }, 'Token验证成功');

  } catch (err) {
    console.error('Token验证错误:', err);
    return serverError(res, 'Token验证失败');
  }
});

// 刷新JWT token
router.post('/refresh', authenticateToken, refreshToken);

// 用户登出（前端删除token即可，这里只是示例）
router.post('/logout', authenticateToken, (req, res) => {
  return success(res, null, '登出成功');
});

module.exports = router;
