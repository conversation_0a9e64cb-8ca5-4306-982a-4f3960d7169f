const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const db = require('../config/database');
const { success, error, unauthorized } = require('../utils/response');
const { validateAdminLogin } = require('../utils/validation');

/**
 * 管理员登录
 */
async function adminLogin(req, res) {
  try {
    // 验证输入数据
    const { error: validationError, value } = validateAdminLogin(req.body);
    if (validationError) {
      return error(res, validationError.details[0].message, 400);
    }

    const { username, password } = value;

    // 查询管理员用户
    const [users] = await db.execute(
      'SELECT id, username, email, password_hash, role, created_at FROM users WHERE (username = ? OR email = ?) AND role = ?',
      [username, username, 'admin']
    );

    if (users.length === 0) {
      return unauthorized(res, '管理员账号或密码错误');
    }

    const user = users[0];

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password_hash);
    if (!isPasswordValid) {
      return unauthorized(res, '管理员账号或密码错误');
    }

    // 生成JWT token (管理员token有效期较短，2小时)
    const token = jwt.sign(
      { 
        id: user.id, 
        username: user.username, 
        email: user.email, 
        role: user.role 
      },
      process.env.JWT_SECRET,
      { expiresIn: '2h' }
    );

    // 记录登录日志
    await logAdminLogin(user.id, req);

    // 返回用户信息和token（不包含密码）
    const userInfo = {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      created_at: user.created_at
    };

    success(res, {
      user: userInfo,
      token: token,
      expires_in: 7200 // 2小时，单位秒
    }, '管理员登录成功');

  } catch (err) {
    console.error('管理员登录错误:', err);
    error(res, '登录失败，请稍后重试', 500);
  }
}

/**
 * 验证管理员token
 */
async function verifyAdminToken(req, res) {
  try {
    // 通过adminAuth中间件后，req.user已包含用户信息
    const user = req.user;

    // 从数据库获取最新的用户信息
    const [users] = await db.execute(
      'SELECT id, username, email, role, created_at, updated_at FROM users WHERE id = ? AND role = ?',
      [user.id, 'admin']
    );

    if (users.length === 0) {
      return unauthorized(res, '管理员账号不存在或权限已变更');
    }

    const userInfo = users[0];

    success(res, {
      user: userInfo
    }, 'Token验证成功');

  } catch (err) {
    console.error('Token验证错误:', err);
    error(res, 'Token验证失败', 500);
  }
}

/**
 * 管理员登出
 */
async function adminLogout(req, res) {
  try {
    // 记录登出日志
    await logAdminLogout(req.user.id, req);

    success(res, null, '管理员登出成功');

  } catch (err) {
    console.error('管理员登出错误:', err);
    error(res, '登出失败', 500);
  }
}

/**
 * 获取管理员个人信息
 */
async function getAdminProfile(req, res) {
  try {
    const [users] = await db.execute(
      'SELECT id, username, email, role, created_at, updated_at FROM users WHERE id = ? AND role = ?',
      [req.user.id, 'admin']
    );

    if (users.length === 0) {
      return unauthorized(res, '管理员账号不存在');
    }

    const user = users[0];

    // 获取最近的登录记录
    const [loginLogs] = await db.execute(
      `SELECT created_at, ip_address FROM admin_logs 
       WHERE admin_id = ? AND action = 'login' 
       ORDER BY created_at DESC LIMIT 5`,
      [user.id]
    );

    success(res, {
      user: user,
      recent_logins: loginLogs
    }, '获取管理员信息成功');

  } catch (err) {
    console.error('获取管理员信息错误:', err);
    error(res, '获取管理员信息失败', 500);
  }
}

/**
 * 修改管理员密码
 */
async function changeAdminPassword(req, res) {
  try {
    const { current_password, new_password, confirm_password } = req.body;

    // 基础验证
    if (!current_password || !new_password || !confirm_password) {
      return error(res, '请填写完整的密码信息', 400);
    }

    if (new_password !== confirm_password) {
      return error(res, '新密码与确认密码不一致', 400);
    }

    if (new_password.length < 6) {
      return error(res, '新密码长度不能少于6位', 400);
    }

    // 获取当前密码
    const [users] = await db.execute(
      'SELECT password_hash FROM users WHERE id = ? AND role = ?',
      [req.user.id, 'admin']
    );

    if (users.length === 0) {
      return unauthorized(res, '管理员账号不存在');
    }

    // 验证当前密码
    const isCurrentPasswordValid = await bcrypt.compare(current_password, users[0].password_hash);
    if (!isCurrentPasswordValid) {
      return error(res, '当前密码错误', 400);
    }

    // 检查新密码是否与当前密码相同
    const isSamePassword = await bcrypt.compare(new_password, users[0].password_hash);
    if (isSamePassword) {
      return error(res, '新密码不能与当前密码相同', 400);
    }

    // 加密新密码
    const saltRounds = 12;
    const newPasswordHash = await bcrypt.hash(new_password, saltRounds);

    // 更新密码
    await db.execute(
      'UPDATE users SET password_hash = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [newPasswordHash, req.user.id]
    );

    success(res, null, '密码修改成功');

  } catch (err) {
    console.error('修改管理员密码错误:', err);
    error(res, '密码修改失败', 500);
  }
}

/**
 * 记录管理员登录日志
 */
async function logAdminLogin(adminId, req) {
  try {
    await db.execute(
      `INSERT INTO admin_logs (admin_id, action, resource, details, ip_address, user_agent) 
       VALUES (?, ?, ?, ?, ?, ?)`,
      [
        adminId,
        'login',
        'admin_auth',
        JSON.stringify({ method: 'POST', url: req.originalUrl || '/api/admin/auth/login' }),
        req.ip || req.connection?.remoteAddress || '127.0.0.1',
        req.get('User-Agent') || 'Unknown'
      ]
    );
  } catch (error) {
    console.error('记录管理员登录日志失败:', error);
  }
}

/**
 * 记录管理员登出日志
 */
async function logAdminLogout(adminId, req) {
  try {
    await db.execute(
      `INSERT INTO admin_logs (admin_id, action, resource, details, ip_address, user_agent) 
       VALUES (?, ?, ?, ?, ?, ?)`,
      [
        adminId,
        'logout',
        'admin_auth',
        JSON.stringify({ method: 'POST', url: req.originalUrl || '/api/admin/auth/logout' }),
        req.ip || req.connection?.remoteAddress || '127.0.0.1',
        req.get('User-Agent') || 'Unknown'
      ]
    );
  } catch (error) {
    console.error('记录管理员登出日志失败:', error);
  }
}

module.exports = {
  adminLogin,
  verifyAdminToken,
  adminLogout,
  getAdminProfile,
  changeAdminPassword
};
