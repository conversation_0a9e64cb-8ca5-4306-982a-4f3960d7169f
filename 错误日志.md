
      at Object.toHaveProperty (tests/admin-dashboard.test.js:47:34)

  ● 管理员仪表盘 API 测试 › GET /api/admin/dashboard › 应该拒绝非管理员用户

    TypeError: Cannot read properties of null (reading 'token')

      84 |         });
      85 |
    > 86 |       const userToken = userLoginResponse.body.data.token;
         |                                                     ^
      87 |
      88 |       const response = await request(app)
      89 |         .get('/api/admin/dashboard')

      at Object.token (tests/admin-dashboard.test.js:86:53)

  ● 管理员仪表盘 API 测试 › GET /api/admin/dashboard/charts › 应该能够获取图表数据

    expect(received).toBe(expected) // Object.is equality

    Expected: 200
    Received: 500

      102 |         .set('Authorization', `Bearer ${adminToken}`);
      103 |
    > 104 |       expect(response.status).toBe(200);
          |                               ^
      105 |       expect(response.body.success).toBe(true);
      106 |       expect(response.body.data).toHaveProperty('sales_trend');
      107 |       expect(response.body.data).toHaveProperty('order_status');

      at Object.toBe (tests/admin-dashboard.test.js:104:31)

  ● 管理员仪表盘 API 测试 › GET /api/admin/dashboard/charts › 应该支持不同的时间周期参数

    expect(received).toBe(expected) // Object.is equality

    Expected: 200
    Received: 500

      120 |           .set('Authorization', `Bearer ${adminToken}`);
      121 |
    > 122 |         expect(response.status).toBe(200);
          |                                 ^
      123 |         expect(response.body.success).toBe(true);
      124 |         expect(response.body.data).toHaveProperty('sales_trend');
      125 |         expect(response.body.data).toHaveProperty('order_status');

      at Object.toBe (tests/admin-dashboard.test.js:122:33)

  ● 管理员仪表盘 API 测试 › GET /api/admin/dashboard/products/ranking › 应该能够获取商品销售排行

    expect(received).toBe(expected) // Object.is equality

    Expected: 200
    Received: 500

      134 |         .set('Authorization', `Bearer ${adminToken}`);
      135 |
    > 136 |       expect(response.status).toBe(200);
          |                               ^
      137 |       expect(response.body.success).toBe(true);
      138 |       expect(response.body.data).toHaveProperty('ranking');
      139 |       expect(Array.isArray(response.body.data.ranking)).toBe(true);

      at Object.toBe (tests/admin-dashboard.test.js:136:31)

  ● 管理员仪表盘 API 测试 › GET /api/admin/dashboard/products/ranking › 应该支持限制返回数量

    expect(received).toBe(expected) // Object.is equality

    Expected: 200
    Received: 500

      157 |         .set('Authorization', `Bearer ${adminToken}`);
      158 |
    > 159 |       expect(response.status).toBe(200);
          |                               ^
      160 |       expect(response.body.success).toBe(true);
      161 |       expect(response.body.data.ranking.length).toBeLessThanOrEqual(5);
      162 |     });

      at Object.toBe (tests/admin-dashboard.test.js:159:31)

  ● 管理员仪表盘 API 测试 › GET /api/admin/dashboard/inventory/alerts › 应该能够获取库存预警信息

    expect(received).toHaveProperty(path)

    Expected path: "threshold"
    Received path: []

    Received value: "获取库存预警成功"

      171 |       expect(response.status).toBe(200);
      172 |       expect(response.body.success).toBe(true);
    > 173 |       expect(response.body.data).toHaveProperty('threshold');
          |                                  ^
      174 |       expect(response.body.data).toHaveProperty('alerts');
      175 |       expect(response.body.data).toHaveProperty('total_alerts');
      176 |

      at Object.toHaveProperty (tests/admin-dashboard.test.js:173:34)

  ● 管理员仪表盘 API 测试 › GET /api/admin/dashboard/users/stats › 应该能够获取用户统计信息

    expect(received).toHaveProperty(path)

    Expected path: "total_users"
    Received path: []

    Received value: "获取用户统计成功"

      199 |       expect(response.status).toBe(200);
      200 |       expect(response.body.success).toBe(true);
    > 201 |       expect(response.body.data).toHaveProperty('total_users');
          |                                  ^
      202 |       expect(response.body.data).toHaveProperty('today_registers');
      203 |       expect(response.body.data).toHaveProperty('month_registers');
      204 |       expect(response.body.data).toHaveProperty('active_users');

      at Object.toHaveProperty (tests/admin-dashboard.test.js:201:34)

 FAIL  tests/admin-users.test.js
  ● Console

    console.log
      测试数据库设置完成

      at log (tests/setup.js:35:13)

    console.error
      获取用户列表错误: Error: Incorrect arguments to mysqld_stmt_execute
          at PromisePool.execute (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\mysql2\lib\promise\pool.js:54:22)
          at Object.execute (C:\Users\<USER>\Desktop\edu全栈\edu_post\src\config\database.js:40:34)
          at execute (C:\Users\<USER>\Desktop\edu全栈\edu_post\src\controllers\adminUserController.js:56:30)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\route.js:149:13)
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\src\middleware\adminAuth.js:51:5)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\route.js:149:13)
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\src\middleware\adminAuth.js:33:5)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\jsonwebtoken\verify.js:261:12
          at getSecret (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\jsonwebtoken\verify.js:97:14)
          at Object.<anonymous>.module.exports [as verify] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\jsonwebtoken\verify.js:101:10)
          at verify (C:\Users\<USER>\Desktop\edu全栈\edu_post\src\middleware\adminAuth.js:16:7)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\route.js:149:13)
          at Route.dispatch (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\route.js:119:3)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:284:15
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at Function.handle (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:175:3)
          at router (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:47:12)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at trim_prefix (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:328:13)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:286:9
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at Function.handle (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:175:3)
          at router (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:47:12)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at trim_prefix (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:328:13)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:286:9
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at urlencodedParser (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\body-parser\lib\types\urlencoded.js:94:7)       
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at trim_prefix (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:328:13)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:286:9
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at jsonParser (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\body-parser\lib\types\json.js:113:7)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at trim_prefix (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:328:13)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:286:9
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at cors (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\cors\lib\index.js:188:7)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\cors\lib\index.js:224:17
          at originCallback (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\cors\lib\index.js:214:15)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\cors\lib\index.js:219:13
          at optionsCallback (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\cors\lib\index.js:199:9)
          at corsMiddleware (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\cors\lib\index.js:204:7)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at trim_prefix (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:328:13)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:286:9
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at expressInit (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\middleware\init.js:40:5)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at trim_prefix (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:328:13)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:286:9
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at query (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\middleware\query.js:45:5)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at trim_prefix (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:328:13)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:286:9
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at Function.handle (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:175:3)
          at Function.handle (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\application.js:181:10)
          at Server.app (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\express.js:39:9)
          at Server.emit (node:events:507:28)
          at parserOnIncoming (node:_http_server:1153:12)
          at HTTPParser.parserOnHeadersComplete (node:_http_common:117:17) {
        code: 'ER_WRONG_ARGUMENTS',
        errno: 1210,
        sql: 'SELECT \n' +
          '         id, username, email, role, created_at, updated_at,\n' +
          '         (SELECT COUNT(*) FROM orders WHERE user_id = users.id) as order_count,\n' +
          "         (SELECT COALESCE(SUM(total_amount), 0) FROM orders WHERE user_id = users.id AND status = 'completed') as total_spent\n" 
+
          '       FROM users \n' +
          '       WHERE 1=1 \n' +
          '       ORDER BY created_at DESC \n' +
          '       LIMIT ? OFFSET ?',
        sqlState: 'HY000',
        sqlMessage: 'Incorrect arguments to mysqld_stmt_execute'
      }

      100 |
      101 |   } catch (err) {
    > 102 |     console.error('获取用户列表错误:', err);
          |             ^
      103 |     error(res, '获取用户列表失败', 500);
      104 |   }
      105 | }

      at error (src/controllers/adminUserController.js:102:13)

    console.error
      获取用户列表错误: Error: Incorrect arguments to mysqld_stmt_execute
          at PromisePool.execute (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\mysql2\lib\promise\pool.js:54:22)
          at Object.execute (C:\Users\<USER>\Desktop\edu全栈\edu_post\src\config\database.js:40:34)
          at execute (C:\Users\<USER>\Desktop\edu全栈\edu_post\src\controllers\adminUserController.js:56:30)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\route.js:149:13)
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\src\middleware\adminAuth.js:51:5)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\route.js:149:13)
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\src\middleware\adminAuth.js:33:5)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\jsonwebtoken\verify.js:261:12
          at getSecret (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\jsonwebtoken\verify.js:97:14)
          at Object.<anonymous>.module.exports [as verify] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\jsonwebtoken\verify.js:101:10)
          at verify (C:\Users\<USER>\Desktop\edu全栈\edu_post\src\middleware\adminAuth.js:16:7)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\route.js:149:13)
          at Route.dispatch (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\route.js:119:3)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:284:15
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at Function.handle (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:175:3)
          at router (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:47:12)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at trim_prefix (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:328:13)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:286:9
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at Function.handle (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:175:3)
          at router (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:47:12)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at trim_prefix (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:328:13)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:286:9
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at urlencodedParser (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\body-parser\lib\types\urlencoded.js:94:7)       
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at trim_prefix (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:328:13)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:286:9
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at jsonParser (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\body-parser\lib\types\json.js:113:7)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at trim_prefix (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:328:13)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:286:9
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at cors (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\cors\lib\index.js:188:7)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\cors\lib\index.js:224:17
          at originCallback (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\cors\lib\index.js:214:15)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\cors\lib\index.js:219:13
          at optionsCallback (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\cors\lib\index.js:199:9)
          at corsMiddleware (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\cors\lib\index.js:204:7)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at trim_prefix (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:328:13)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:286:9
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at expressInit (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\middleware\init.js:40:5)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at trim_prefix (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:328:13)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:286:9
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at query (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\middleware\query.js:45:5)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at trim_prefix (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:328:13)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:286:9
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at Function.handle (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:175:3)
          at Function.handle (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\application.js:181:10)
          at Server.app (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\express.js:39:9)
          at Server.emit (node:events:507:28)
          at parserOnIncoming (node:_http_server:1153:12)
          at HTTPParser.parserOnHeadersComplete (node:_http_common:117:17) {
        code: 'ER_WRONG_ARGUMENTS',
        errno: 1210,
        sql: 'SELECT \n' +
          '         id, username, email, role, created_at, updated_at,\n' +
          '         (SELECT COUNT(*) FROM orders WHERE user_id = users.id) as order_count,\n' +
          "         (SELECT COALESCE(SUM(total_amount), 0) FROM orders WHERE user_id = users.id AND status = 'completed') as total_spent\n" 
+
          '       FROM users \n' +
          '       WHERE 1=1 AND (username LIKE ? OR email LIKE ?) \n' +
          '       ORDER BY created_at DESC \n' +
          '       LIMIT ? OFFSET ?',
        sqlState: 'HY000',
        sqlMessage: 'Incorrect arguments to mysqld_stmt_execute'
      }

      100 |
      101 |   } catch (err) {
    > 102 |     console.error('获取用户列表错误:', err);
          |             ^
      103 |     error(res, '获取用户列表失败', 500);
      104 |   }
      105 | }

      at error (src/controllers/adminUserController.js:102:13)

    console.error
      获取用户列表错误: Error: Incorrect arguments to mysqld_stmt_execute
          at PromisePool.execute (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\mysql2\lib\promise\pool.js:54:22)
          at Object.execute (C:\Users\<USER>\Desktop\edu全栈\edu_post\src\config\database.js:40:34)
          at execute (C:\Users\<USER>\Desktop\edu全栈\edu_post\src\controllers\adminUserController.js:56:30)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\route.js:149:13)
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\src\middleware\adminAuth.js:51:5)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\route.js:149:13)
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\src\middleware\adminAuth.js:33:5)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\jsonwebtoken\verify.js:261:12
          at getSecret (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\jsonwebtoken\verify.js:97:14)
          at Object.<anonymous>.module.exports [as verify] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\jsonwebtoken\verify.js:101:10)
          at verify (C:\Users\<USER>\Desktop\edu全栈\edu_post\src\middleware\adminAuth.js:16:7)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\route.js:149:13)
          at Route.dispatch (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\route.js:119:3)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:284:15
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at Function.handle (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:175:3)
          at router (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:47:12)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at trim_prefix (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:328:13)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:286:9
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at Function.handle (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:175:3)
          at router (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:47:12)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at trim_prefix (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:328:13)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:286:9
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at urlencodedParser (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\body-parser\lib\types\urlencoded.js:94:7)       
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at trim_prefix (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:328:13)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:286:9
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at jsonParser (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\body-parser\lib\types\json.js:113:7)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at trim_prefix (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:328:13)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:286:9
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at cors (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\cors\lib\index.js:188:7)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\cors\lib\index.js:224:17
          at originCallback (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\cors\lib\index.js:214:15)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\cors\lib\index.js:219:13
          at optionsCallback (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\cors\lib\index.js:199:9)
          at corsMiddleware (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\cors\lib\index.js:204:7)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at trim_prefix (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:328:13)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:286:9
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at expressInit (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\middleware\init.js:40:5)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at trim_prefix (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:328:13)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:286:9
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at query (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\middleware\query.js:45:5)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at trim_prefix (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:328:13)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:286:9
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at Function.handle (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:175:3)
          at Function.handle (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\application.js:181:10)
          at Server.app (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\express.js:39:9)
          at Server.emit (node:events:507:28)
          at parserOnIncoming (node:_http_server:1153:12)
          at HTTPParser.parserOnHeadersComplete (node:_http_common:117:17) {
        code: 'ER_WRONG_ARGUMENTS',
        errno: 1210,
        sql: 'SELECT \n' +
          '         id, username, email, role, created_at, updated_at,\n' +
          '         (SELECT COUNT(*) FROM orders WHERE user_id = users.id) as order_count,\n' +
          "         (SELECT COALESCE(SUM(total_amount), 0) FROM orders WHERE user_id = users.id AND status = 'completed') as total_spent\n" 
+
          '       FROM users \n' +
          '       WHERE 1=1 AND role = ? \n' +
          '       ORDER BY created_at DESC \n' +
          '       LIMIT ? OFFSET ?',
        sqlState: 'HY000',
        sqlMessage: 'Incorrect arguments to mysqld_stmt_execute'
      }

      100 |
      101 |   } catch (err) {
    > 102 |     console.error('获取用户列表错误:', err);
          |             ^
      103 |     error(res, '获取用户列表失败', 500);
      104 |   }
      105 | }

      at error (src/controllers/adminUserController.js:102:13)

    console.error
      获取用户列表错误: Error: Incorrect arguments to mysqld_stmt_execute
          at PromisePool.execute (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\mysql2\lib\promise\pool.js:54:22)
          at Object.execute (C:\Users\<USER>\Desktop\edu全栈\edu_post\src\config\database.js:40:34)
          at execute (C:\Users\<USER>\Desktop\edu全栈\edu_post\src\controllers\adminUserController.js:56:30)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\route.js:149:13)
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\src\middleware\adminAuth.js:51:5)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\route.js:149:13)
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\src\middleware\adminAuth.js:33:5)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\jsonwebtoken\verify.js:261:12
          at getSecret (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\jsonwebtoken\verify.js:97:14)
          at Object.<anonymous>.module.exports [as verify] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\jsonwebtoken\verify.js:101:10)
          at verify (C:\Users\<USER>\Desktop\edu全栈\edu_post\src\middleware\adminAuth.js:16:7)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\route.js:149:13)
          at Route.dispatch (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\route.js:119:3)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:284:15
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at Function.handle (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:175:3)
          at router (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:47:12)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at trim_prefix (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:328:13)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:286:9
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at Function.handle (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:175:3)
          at router (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:47:12)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at trim_prefix (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:328:13)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:286:9
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at urlencodedParser (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\body-parser\lib\types\urlencoded.js:94:7)       
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at trim_prefix (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:328:13)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:286:9
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at jsonParser (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\body-parser\lib\types\json.js:113:7)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at trim_prefix (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:328:13)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:286:9
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at cors (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\cors\lib\index.js:188:7)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\cors\lib\index.js:224:17
          at originCallback (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\cors\lib\index.js:214:15)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\cors\lib\index.js:219:13
          at optionsCallback (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\cors\lib\index.js:199:9)
          at corsMiddleware (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\cors\lib\index.js:204:7)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at trim_prefix (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:328:13)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:286:9
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at expressInit (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\middleware\init.js:40:5)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at trim_prefix (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:328:13)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:286:9
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at query (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\middleware\query.js:45:5)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at trim_prefix (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:328:13)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:286:9
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at Function.handle (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:175:3)
          at Function.handle (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\application.js:181:10)
          at Server.app (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\express.js:39:9)
          at Server.emit (node:events:507:28)
          at parserOnIncoming (node:_http_server:1153:12)
          at HTTPParser.parserOnHeadersComplete (node:_http_common:117:17) {
        code: 'ER_WRONG_ARGUMENTS',
        errno: 1210,
        sql: 'SELECT \n' +
          '         id, username, email, role, created_at, updated_at,\n' +
          '         (SELECT COUNT(*) FROM orders WHERE user_id = users.id) as order_count,\n' +
          "         (SELECT COALESCE(SUM(total_amount), 0) FROM orders WHERE user_id = users.id AND status = 'completed') as total_spent\n" 
+
          '       FROM users \n' +
          '       WHERE 1=1 \n' +
          '       ORDER BY created_at DESC \n' +
          '       LIMIT ? OFFSET ?',
        sqlState: 'HY000',
        sqlMessage: 'Incorrect arguments to mysqld_stmt_execute'
      }

      100 |
      101 |   } catch (err) {
    > 102 |     console.error('获取用户列表错误:', err);
          |             ^
      103 |     error(res, '获取用户列表失败', 500);
      104 |   }
      105 | }

      at error (src/controllers/adminUserController.js:102:13)

    console.error
      获取用户列表错误: Error: Incorrect arguments to mysqld_stmt_execute
          at PromisePool.execute (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\mysql2\lib\promise\pool.js:54:22)
          at Object.execute (C:\Users\<USER>\Desktop\edu全栈\edu_post\src\config\database.js:40:34)
          at execute (C:\Users\<USER>\Desktop\edu全栈\edu_post\src\controllers\adminUserController.js:56:30)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\route.js:149:13)
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\src\middleware\adminAuth.js:51:5)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\route.js:149:13)
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\src\middleware\adminAuth.js:33:5)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\jsonwebtoken\verify.js:261:12
          at getSecret (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\jsonwebtoken\verify.js:97:14)
          at Object.<anonymous>.module.exports [as verify] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\jsonwebtoken\verify.js:101:10)
          at verify (C:\Users\<USER>\Desktop\edu全栈\edu_post\src\middleware\adminAuth.js:16:7)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\route.js:149:13)
          at Route.dispatch (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\route.js:119:3)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:284:15
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at Function.handle (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:175:3)
          at router (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:47:12)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at trim_prefix (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:328:13)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:286:9
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at Function.handle (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:175:3)
          at router (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:47:12)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at trim_prefix (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:328:13)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:286:9
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at urlencodedParser (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\body-parser\lib\types\urlencoded.js:94:7)       
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at trim_prefix (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:328:13)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:286:9
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at jsonParser (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\body-parser\lib\types\json.js:113:7)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at trim_prefix (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:328:13)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:286:9
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at cors (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\cors\lib\index.js:188:7)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\cors\lib\index.js:224:17
          at originCallback (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\cors\lib\index.js:214:15)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\cors\lib\index.js:219:13
          at optionsCallback (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\cors\lib\index.js:199:9)
          at corsMiddleware (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\cors\lib\index.js:204:7)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at trim_prefix (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:328:13)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:286:9
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at expressInit (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\middleware\init.js:40:5)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at trim_prefix (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:328:13)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:286:9
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at query (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\middleware\query.js:45:5)
          at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\layer.js:95:5)
          at trim_prefix (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:328:13)
          at C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:286:9
          at Function.process_params (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:346:12)      
          at next (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:280:10)
          at Function.handle (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\router\index.js:175:3)
          at Function.handle (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\application.js:181:10)
          at Server.app (C:\Users\<USER>\Desktop\edu全栈\edu_post\node_modules\express\lib\express.js:39:9)
          at Server.emit (node:events:507:28)
          at parserOnIncoming (node:_http_server:1153:12)
          at HTTPParser.parserOnHeadersComplete (node:_http_common:117:17) {
        code: 'ER_WRONG_ARGUMENTS',
        errno: 1210,
        sql: 'SELECT \n' +
          '         id, username, email, role, created_at, updated_at,\n' +
          '         (SELECT COUNT(*) FROM orders WHERE user_id = users.id) as order_count,\n' +
          "         (SELECT COALESCE(SUM(total_amount), 0) FROM orders WHERE user_id = users.id AND status = 'completed') as total_spent\n" 
+
          '       FROM users \n' +
          '       WHERE 1=1 \n' +
          '       ORDER BY created_at DESC \n' +
          '       LIMIT ? OFFSET ?',
        sqlState: 'HY000',
        sqlMessage: 'Incorrect arguments to mysqld_stmt_execute'
      }

      100 |
      101 |   } catch (err) {
    > 102 |     console.error('获取用户列表错误:', err);
          |             ^
      103 |     error(res, '获取用户列表失败', 500);
      104 |   }
      105 | }

      at error (src/controllers/adminUserController.js:102:13)

    console.error
      获取用户详情错误: TypeError: notFound is not a function
          at notFound (C:\Users\<USER>\Desktop\edu全栈\edu_post\src\controllers\adminUserController.js:121:14)
          at processTicksAndRejections (node:internal/process/task_queues:105:5)

      177 |
      178 |   } catch (err) {
    > 179 |     console.error('获取用户详情错误:', err);
          |             ^
      180 |     error(res, '获取用户详情失败', 500);
      181 |   }
      182 | }

      at error (src/controllers/adminUserController.js:179:13)

    console.error
      更新用户信息错误: TypeError: notFound is not a function
          at notFound (C:\Users\<USER>\Desktop\edu全栈\edu_post\src\controllers\adminUserController.js:253:14)
          at processTicksAndRejections (node:internal/process/task_queues:105:5)

      311 |
      312 |   } catch (err) {
    > 313 |     console.error('更新用户信息错误:', err);
          |             ^
      314 |     error(res, '更新用户信息失败', 500);
      315 |   }
      316 | }

      at error (src/controllers/adminUserController.js:313:13)

    console.error
      更新用户信息错误: TypeError: notFound is not a function
          at notFound (C:\Users\<USER>\Desktop\edu全栈\edu_post\src\controllers\adminUserController.js:253:14)
          at processTicksAndRejections (node:internal/process/task_queues:105:5)

      311 |
      312 |   } catch (err) {
    > 313 |     console.error('更新用户信息错误:', err);
          |             ^
      314 |     error(res, '更新用户信息失败', 500);
      315 |   }
      316 | }

      at error (src/controllers/adminUserController.js:313:13)

    console.error
      更新用户信息错误: TypeError: notFound is not a function
          at notFound (C:\Users\<USER>\Desktop\edu全栈\edu_post\src\controllers\adminUserController.js:253:14)
          at processTicksAndRejections (node:internal/process/task_queues:105:5)

      311 |
      312 |   } catch (err) {
    > 313 |     console.error('更新用户信息错误:', err);
          |             ^
      314 |     error(res, '更新用户信息失败', 500);
      315 |   }
      316 | }

      at error (src/controllers/adminUserController.js:313:13)

    console.error
      删除用户错误: TypeError: notFound is not a function
          at notFound (C:\Users\<USER>\Desktop\edu全栈\edu_post\src\controllers\adminUserController.js:372:14)
          at processTicksAndRejections (node:internal/process/task_queues:105:5)

      408 |
      409 |   } catch (err) {
    > 410 |     console.error('删除用户错误:', err);
          |             ^
      411 |     error(res, '删除用户失败', 500);
      412 |   }
      413 | }

      at error (src/controllers/adminUserController.js:410:13)

    console.log
      测试数据清理完成

      at log (tests/setup.js:54:13)

  ● 管理员用户管理 API 测试 › GET /api/admin/users › 应该能够获取用户列表

    expect(received).toBe(expected) // Object.is equality

    Expected: 200
    Received: 500

      42 |         .set('Authorization', `Bearer ${adminToken}`);
      43 |
    > 44 |       expect(response.status).toBe(200);
         |                               ^
      45 |       expect(response.body.success).toBe(true);
      46 |       expect(response.body.data).toHaveProperty('users');
      47 |       expect(response.body.data).toHaveProperty('pagination');

      at Object.toBe (tests/admin-users.test.js:44:31)

  ● 管理员用户管理 API 测试 › GET /api/admin/users › 应该支持搜索功能

    expect(received).toBe(expected) // Object.is equality

    Expected: 200
    Received: 500

      73 |         .set('Authorization', `Bearer ${adminToken}`);
      74 |
    > 75 |       expect(response.status).toBe(200);
         |                               ^
      76 |       expect(response.body.success).toBe(true);
      77 |
      78 |       // 搜索结果应该包含搜索关键词

      at Object.toBe (tests/admin-users.test.js:75:31)

  ● 管理员用户管理 API 测试 › GET /api/admin/users › 应该支持角色筛选

    expect(received).toBe(expected) // Object.is equality

    Expected: 200
    Received: 500

      91 |         .set('Authorization', `Bearer ${adminToken}`);
      92 |
    > 93 |       expect(response.status).toBe(200);
         |                               ^
      94 |       expect(response.body.success).toBe(true);
      95 |
      96 |       // 所有返回的用户都应该是customer角色

      at Object.toBe (tests/admin-users.test.js:93:31)

  ● 管理员用户管理 API 测试 › GET /api/admin/users › 应该支持分页

    expect(received).toBe(expected) // Object.is equality

    Expected: 200
    Received: 500

      106 |         .set('Authorization', `Bearer ${adminToken}`);
      107 |
    > 108 |       expect(response.status).toBe(200);
          |                               ^
      109 |       expect(response.body.success).toBe(true);
      110 |       expect(response.body.data.pagination.per_page).toBe(5);
      111 |       expect(response.body.data.users.length).toBeLessThanOrEqual(5);

      at Object.toBe (tests/admin-users.test.js:108:31)

  ● 管理员用户管理 API 测试 › POST /api/admin/users › 应该能够创建新用户

    expect(received).toHaveProperty(path)

    Expected path: "user"
    Received path: []

    Received value: "用户创建成功"

      137 |       expect(response.status).toBe(201);
      138 |       expect(response.body.success).toBe(true);
    > 139 |       expect(response.body.data).toHaveProperty('user');
          |                                  ^
      140 |       expect(response.body.data.user.username).toBe(newUser.username);
      141 |       expect(response.body.data.user.email).toBe(newUser.email);
      142 |       expect(response.body.data.user.role).toBe(newUser.role);

      at Object.toHaveProperty (tests/admin-users.test.js:139:34)

  ● 管理员用户管理 API 测试 › GET /api/admin/users/:id › 应该能够获取用户详情

    expect(received).toBe(expected) // Object.is equality

    Expected: 200
    Received: 500

      205 |         .set('Authorization', `Bearer ${adminToken}`);
      206 |
    > 207 |       expect(response.status).toBe(200);
          |                               ^
      208 |       expect(response.body.success).toBe(true);
      209 |       expect(response.body.data).toHaveProperty('user');
      210 |       expect(response.body.data.user.id).toBe(testUserId);

      at Object.toBe (tests/admin-users.test.js:207:31)

  ● 管理员用户管理 API 测试 › GET /api/admin/users/:id › 应该返回404对于不存在的用户

    expect(received).toBe(expected) // Object.is equality

    Expected: 404
    Received: 500

      224 |         .set('Authorization', `Bearer ${adminToken}`);
      225 |
    > 226 |       expect(response.status).toBe(404);
          |                               ^
      227 |       expect(response.body.success).toBe(false);
      228 |     });
      229 |   });

      at Object.toBe (tests/admin-users.test.js:226:31)

  ● 管理员用户管理 API 测试 › PUT /api/admin/users/:id › 应该能够更新用户信息

    expect(received).toBe(expected) // Object.is equality

    Expected: 200
    Received: 404

      241 |         .send(updateData);
      242 |
    > 243 |       expect(response.status).toBe(200);
          |                               ^
      244 |       expect(response.body.success).toBe(true);
      245 |       expect(response.body.data.user.username).toBe(updateData.username);
      246 |       expect(response.body.data.user.email).toBe(updateData.email);

      at Object.toBe (tests/admin-users.test.js:243:31)

  ● 管理员用户管理 API 测试 › PUT /api/admin/users/:id › 应该能够更新用户角色

    expect(received).toBe(expected) // Object.is equality

    Expected: 200
    Received: 404

      257 |         .send(updateData);
      258 |
    > 259 |       expect(response.status).toBe(200);
          |                               ^
      260 |       expect(response.body.success).toBe(true);
      261 |       expect(response.body.data.user.role).toBe('admin');
      262 |     });

      at Object.toBe (tests/admin-users.test.js:259:31)

  ● 管理员用户管理 API 测试 › PUT /api/admin/users/:id › 应该返回404对于不存在的用户

    expect(received).toBe(expected) // Object.is equality

    Expected: 404
    Received: 500

      268 |         .send({ username: 'test' });
      269 |
    > 270 |       expect(response.status).toBe(404);
          |                               ^
      271 |       expect(response.body.success).toBe(false);
      272 |     });
      273 |   });

      at Object.toBe (tests/admin-users.test.js:270:31)

  ● 管理员用户管理 API 测试 › PUT /api/admin/users/:id/password › 应该能够重置用户密码

    expect(received).toBe(expected) // Object.is equality

    Expected: 200
    Received: 500

      280 |         .send({ new_password: 'newpassword123' });
      281 |
    > 282 |       expect(response.status).toBe(200);
          |                               ^
      283 |       expect(response.body.success).toBe(true);
      284 |     });
      285 |

      at Object.toBe (tests/admin-users.test.js:282:31)

  ● 管理员用户管理 API 测试 › PUT /api/admin/users/:id/password › 应该验证密码长度

    expect(received).toBe(expected) // Object.is equality

    Expected: 400
    Received: 500

      290 |         .send({ new_password: '123' });
      291 |
    > 292 |       expect(response.status).toBe(400);
          |                               ^
      293 |       expect(response.body.success).toBe(false);
      294 |       expect(response.body.message).toContain('密码长度');
      295 |     });

      at Object.toBe (tests/admin-users.test.js:292:31)

  ● 管理员用户管理 API 测试 › DELETE /api/admin/users/:id › 应该能够删除用户

    expect(received).toBe(expected) // Object.is equality

    Expected: 200
    Received: 404

      302 |         .set('Authorization', `Bearer ${adminToken}`);
      303 |
    > 304 |       expect(response.status).toBe(200);
          |                               ^
      305 |       expect(response.body.success).toBe(true);
      306 |
      307 |       // 验证用户已被删除

      at Object.toBe (tests/admin-users.test.js:304:31)

  ● 管理员用户管理 API 测试 › DELETE /api/admin/users/:id › 应该返回404对于不存在的用户

    expect(received).toBe(expected) // Object.is equality

    Expected: 404
    Received: 500

      318 |         .set('Authorization', `Bearer ${adminToken}`);
      319 |
    > 320 |       expect(response.status).toBe(404);
          |                               ^
      321 |       expect(response.body.success).toBe(false);
      322 |     });
      323 |   });

      at Object.toBe (tests/admin-users.test.js:320:31)

A worker process has failed to exit gracefully and has been force exited. This is likely caused by tests leaking due to improper teardown. Try running with --detectOpenHandles to find leaks. Active timers can also cause this, ensure that .unref() was called on them.
Test Suites: 3 failed, 2 passed, 5 total
Tests:       26 failed, 18 passed, 44 total
Snapshots:   0 total
Time:        3.888 s
Ran all test suites.
PS C:\Users\<USER>\Desktop\edu全栈\edu_post> 