const jwt = require('jsonwebtoken');
const { unauthorized, forbidden } = require('../utils/response');

/**
 * 管理员专用认证中间件
 * 组合了token验证和管理员权限检查
 */
function adminAuth(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    return unauthorized(res, '访问令牌缺失，请先登录管理后台');
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      if (err.name === 'TokenExpiredError') {
        return unauthorized(res, '访问令牌已过期，请重新登录');
      } else if (err.name === 'JsonWebTokenError') {
        return unauthorized(res, '访问令牌无效');
      } else {
        return unauthorized(res, '访问令牌验证失败');
      }
    }

    // 验证用户角色
    if (user.role !== 'admin') {
      return forbidden(res, '权限不足，需要管理员权限');
    }

    req.user = user;
    next();
  });
}

/**
 * 记录管理员操作日志的中间件
 */
function logAdminAction(action, resource) {
  return (req, res, next) => {
    // 在响应完成后记录日志
    const originalSend = res.send;
    res.send = function(data) {
      // 只在成功操作时记录日志
      if (res.statusCode >= 200 && res.statusCode < 300) {
        logOperation(req.user.id, action, resource, req, res);
      }
      originalSend.call(this, data);
    };
    next();
  };
}

/**
 * 记录操作日志到数据库
 */
async function logOperation(adminId, action, resource, req, res) {
  try {
    const db = require('../config/database');
    
    const logData = {
      admin_id: adminId,
      action: action,
      resource: resource,
      resource_id: req.params.id || req.params.orderNumber || null,
      details: JSON.stringify({
        method: req.method,
        url: req.originalUrl,
        body: req.body,
        query: req.query,
        params: req.params
      }),
      ip_address: req.ip || req.connection?.remoteAddress || '127.0.0.1',
      user_agent: req.get('User-Agent') || 'Unknown'
    };

    await db.execute(
      `INSERT INTO admin_logs (admin_id, action, resource, resource_id, details, ip_address, user_agent) 
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [
        logData.admin_id,
        logData.action,
        logData.resource,
        logData.resource_id,
        logData.details,
        logData.ip_address,
        logData.user_agent
      ]
    );
  } catch (error) {
    console.error('记录管理员操作日志失败:', error);
    // 不影响主要业务流程
  }
}

/**
 * 验证管理员权限级别（为将来扩展准备）
 */
function requirePermission(permission) {
  return (req, res, next) => {
    // 目前所有admin都有全部权限
    // 将来可以扩展为基于权限的访问控制
    if (req.user.role !== 'admin') {
      return forbidden(res, '权限不足');
    }
    next();
  };
}

module.exports = {
  adminAuth,
  logAdminAction,
  requirePermission
};
