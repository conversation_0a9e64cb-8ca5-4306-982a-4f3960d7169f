const request = require('supertest');
const express = require('express');
const cors = require('cors');

// 创建测试应用
const app = express();
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 导入路由
const adminRoutes = require('../src/routes/admin');
app.use('/api/admin', adminRoutes);

describe('基础测试', () => {
  test('应该能够访问管理员登录接口', async () => {
    const response = await request(app)
      .post('/api/admin/auth/login')
      .send({
        username: 'admin',
        password: 'admin123'
      });

    // 不管结果如何，至少应该有响应
    expect(response.status).toBeDefined();
    expect(response.body).toBeDefined();
  });
});
