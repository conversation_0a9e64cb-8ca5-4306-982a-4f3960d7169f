const request = require('supertest');
const express = require('express');
const cors = require('cors');
const { setupTestDatabase, cleanupTestDatabase } = require('./setup');

// 创建测试应用
const app = express();
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 导入路由
const authRoutes = require('../src/routes/auth');
const adminRoutes = require('../src/routes/admin');
app.use('/api/auth', authRoutes);
app.use('/api/admin', adminRoutes);

describe('管理员认证 API 测试', () => {
  let adminToken = '';

  beforeAll(async () => {
    await setupTestDatabase();
  });

  afterAll(async () => {
    await cleanupTestDatabase();
  });

  describe('POST /api/admin/auth/login', () => {
    test('应该能够使用正确的管理员账号登录', async () => {
      const response = await request(app)
        .post('/api/admin/auth/login')
        .send({
          username: 'testadmin',
          password: 'admin123'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('token');
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data.user.role).toBe('admin');
      expect(response.body.data.user.username).toBe('testadmin');

      // 保存token用于后续测试
      adminToken = response.body.data.token;
    });

    test('应该拒绝错误的密码', async () => {
      const response = await request(app)
        .post('/api/admin/auth/login')
        .send({
          username: 'testadmin',
          password: 'wrongpassword'
        });

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('密码错误');
    });

    test('应该拒绝不存在的管理员账号', async () => {
      const response = await request(app)
        .post('/api/admin/auth/login')
        .send({
          username: 'nonexistentadmin',
          password: 'admin123'
        });

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('账号或密码错误');
    });

    test('应该拒绝普通用户登录管理后台', async () => {
      const response = await request(app)
        .post('/api/admin/auth/login')
        .send({
          username: 'testuser',
          password: 'user123'
        });

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('账号或密码错误');
    });

    test('应该验证必填字段', async () => {
      const response = await request(app)
        .post('/api/admin/auth/login')
        .send({
          username: '',
          password: ''
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/admin/auth/verify', () => {
    test('应该能够验证有效的管理员token', async () => {
      const response = await request(app)
        .get('/api/admin/auth/verify')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data.user.role).toBe('admin');
    });

    test('应该拒绝无效的token', async () => {
      const response = await request(app)
        .get('/api/admin/auth/verify')
        .set('Authorization', 'Bearer invalidtoken');

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });

    test('应该拒绝缺少token的请求', async () => {
      const response = await request(app)
        .get('/api/admin/auth/verify');

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('访问令牌缺失');
    });
  });

  describe('GET /api/admin/auth/profile', () => {
    test('应该能够获取管理员个人信息', async () => {
      const response = await request(app)
        .get('/api/admin/auth/profile')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data.user.username).toBe('testadmin');
      expect(response.body.data.user.role).toBe('admin');
    });

    test('应该拒绝未认证的请求', async () => {
      const response = await request(app)
        .get('/api/admin/auth/profile');

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });
  });

  describe('PUT /api/admin/auth/password', () => {
    test('应该能够修改管理员密码', async () => {
      const response = await request(app)
        .put('/api/admin/auth/password')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          current_password: 'admin123',
          new_password: 'newadmin123',
          confirm_password: 'newadmin123'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);

      // 验证新密码可以登录
      const loginResponse = await request(app)
        .post('/api/admin/auth/login')
        .send({
          username: 'testadmin',
          password: 'newadmin123'
        });

      expect(loginResponse.status).toBe(200);
      expect(loginResponse.body.success).toBe(true);

      // 更新token
      adminToken = loginResponse.body.data.token;
    });

    test('应该拒绝错误的当前密码', async () => {
      const response = await request(app)
        .put('/api/admin/auth/password')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          current_password: 'wrongpassword',
          new_password: 'newadmin123',
          confirm_password: 'newadmin123'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('当前密码错误');
    });

    test('应该拒绝不匹配的确认密码', async () => {
      const response = await request(app)
        .put('/api/admin/auth/password')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          current_password: 'newadmin123',
          new_password: 'newpassword',
          confirm_password: 'differentpassword'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('确认密码不一致');
    });
  });

  describe('POST /api/admin/auth/logout', () => {
    test('应该能够成功登出', async () => {
      const response = await request(app)
        .post('/api/admin/auth/logout')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('登出成功');
    });

    test('应该拒绝未认证的登出请求', async () => {
      const response = await request(app)
        .post('/api/admin/auth/logout');

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });
  });
});
