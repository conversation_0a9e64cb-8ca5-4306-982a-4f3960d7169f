const bcrypt = require('bcryptjs');
const db = require('../config/database');
const { success, error } = require('../utils/response');

/**
 * 获取用户列表
 */
async function getUserList(req, res) {
  try {
    const {
      page = 1,
      limit = 20,
      search = '',
      role = 'all',
      sort = 'created_desc'
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);
    
    // 构建查询条件
    let whereClause = 'WHERE 1=1';
    let queryParams = [];

    // 搜索条件
    if (search) {
      whereClause += ' AND (username LIKE ? OR email LIKE ?)';
      queryParams.push(`%${search}%`, `%${search}%`);
    }

    // 角色筛选
    if (role !== 'all') {
      whereClause += ' AND role = ?';
      queryParams.push(role);
    }

    // 排序条件
    let orderClause = 'ORDER BY ';
    switch (sort) {
      case 'created_asc':
        orderClause += 'created_at ASC';
        break;
      case 'created_desc':
        orderClause += 'created_at DESC';
        break;
      case 'username_asc':
        orderClause += 'username ASC';
        break;
      case 'username_desc':
        orderClause += 'username DESC';
        break;
      default:
        orderClause += 'created_at DESC';
    }

    // 查询用户列表
    const limitValue = parseInt(limit);
    const offsetValue = parseInt(offset);

    const [users] = await db.execute(
      `SELECT
         id, username, email, role, created_at, updated_at,
         (SELECT COUNT(*) FROM orders WHERE user_id = users.id) as order_count,
         (SELECT COALESCE(SUM(total_amount), 0) FROM orders WHERE user_id = users.id AND status = 'completed') as total_spent
       FROM users
       ${whereClause}
       ${orderClause}
       LIMIT ? OFFSET ?`,
      [...queryParams, limitValue, offsetValue]
    );

    // 查询总数
    const [countResult] = await db.execute(
      `SELECT COUNT(*) as total FROM users ${whereClause}`,
      queryParams
    );

    const total = countResult[0].total;
    const totalPages = Math.ceil(total / parseInt(limit));

    // 格式化用户数据
    const formattedUsers = users.map(user => ({
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      order_count: user.order_count,
      total_spent: parseFloat(user.total_spent),
      created_at: user.created_at,
      updated_at: user.updated_at
    }));

    success(res, {
      users: formattedUsers,
      pagination: {
        current_page: parseInt(page),
        per_page: parseInt(limit),
        total: total,
        total_pages: totalPages,
        has_next: parseInt(page) < totalPages,
        has_prev: parseInt(page) > 1
      }
    }, '获取用户列表成功');

  } catch (err) {
    console.error('获取用户列表错误:', err);
    error(res, '获取用户列表失败', 500);
  }
}

/**
 * 获取用户详情
 */
async function getUserDetail(req, res) {
  try {
    const { id } = req.params;

    // 查询用户基本信息
    const [users] = await db.execute(
      'SELECT id, username, email, role, created_at, updated_at FROM users WHERE id = ?',
      [id]
    );

    if (users.length === 0) {
      return error(res, '用户不存在', 404);
    }

    const user = users[0];

    // 查询用户订单统计
    const [orderStats] = await db.execute(
      `SELECT 
         COUNT(*) as total_orders,
         COUNT(CASE WHEN status = 'pending_payment' THEN 1 END) as pending_orders,
         COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_orders,
         COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_orders,
         COALESCE(SUM(CASE WHEN status = 'completed' THEN total_amount ELSE 0 END), 0) as total_spent
       FROM orders 
       WHERE user_id = ?`,
      [id]
    );

    // 查询最近订单
    const [recentOrders] = await db.execute(
      `SELECT 
         id, order_number, total_amount, status, payment_method, created_at
       FROM orders 
       WHERE user_id = ? 
       ORDER BY created_at DESC 
       LIMIT 10`,
      [id]
    );

    // 查询购物车信息
    const [cartInfo] = await db.execute(
      `SELECT COUNT(*) as cart_items FROM shopping_cart_items WHERE user_id = ?`,
      [id]
    );

    const userDetail = {
      ...user,
      statistics: {
        total_orders: orderStats[0].total_orders,
        pending_orders: orderStats[0].pending_orders,
        completed_orders: orderStats[0].completed_orders,
        cancelled_orders: orderStats[0].cancelled_orders,
        total_spent: parseFloat(orderStats[0].total_spent),
        cart_items: cartInfo[0].cart_items
      },
      recent_orders: recentOrders.map(order => ({
        id: order.id,
        order_number: order.order_number,
        total_amount: parseFloat(order.total_amount),
        status: order.status,
        payment_method: order.payment_method,
        created_at: order.created_at
      }))
    };

    success(res, { user: userDetail }, '获取用户详情成功');

  } catch (err) {
    console.error('获取用户详情错误:', err);
    error(res, '获取用户详情失败', 500);
  }
}

/**
 * 创建用户
 */
async function createUser(req, res) {
  try {
    const { username, email, password, role = 'customer' } = req.body;

    // 基础验证
    if (!username || !email || !password) {
      return error(res, '用户名、邮箱和密码不能为空', 400);
    }

    if (password.length < 6) {
      return error(res, '密码长度不能少于6位', 400);
    }

    if (!['customer', 'admin'].includes(role)) {
      return error(res, '用户角色无效', 400);
    }

    // 检查用户名和邮箱是否已存在
    const [existingUsers] = await db.execute(
      'SELECT id FROM users WHERE username = ? OR email = ?',
      [username, email]
    );

    if (existingUsers.length > 0) {
      return error(res, '用户名或邮箱已存在', 400);
    }

    // 加密密码
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // 创建用户
    const [result] = await db.execute(
      'INSERT INTO users (username, email, password_hash, role) VALUES (?, ?, ?, ?)',
      [username, email, passwordHash, role]
    );

    // 获取创建的用户信息
    const [newUser] = await db.execute(
      'SELECT id, username, email, role, created_at FROM users WHERE id = ?',
      [result.insertId]
    );

    success(res, { user: newUser[0] }, '用户创建成功', 201);

  } catch (err) {
    console.error('创建用户错误:', err);
    error(res, '创建用户失败', 500);
  }
}

/**
 * 更新用户信息
 */
async function updateUser(req, res) {
  try {
    const { id } = req.params;
    const { username, email, role } = req.body;

    // 检查用户是否存在
    const [users] = await db.execute(
      'SELECT id FROM users WHERE id = ?',
      [id]
    );

    if (users.length === 0) {
      return error(res, '用户不存在', 404);
    }

    // 构建更新字段
    let updateFields = [];
    let updateValues = [];

    if (username) {
      // 检查用户名是否已被其他用户使用
      const [existingUsers] = await db.execute(
        'SELECT id FROM users WHERE username = ? AND id != ?',
        [username, id]
      );
      if (existingUsers.length > 0) {
        return error(res, '用户名已被使用', 400);
      }
      updateFields.push('username = ?');
      updateValues.push(username);
    }

    if (email) {
      // 检查邮箱是否已被其他用户使用
      const [existingUsers] = await db.execute(
        'SELECT id FROM users WHERE email = ? AND id != ?',
        [email, id]
      );
      if (existingUsers.length > 0) {
        return error(res, '邮箱已被使用', 400);
      }
      updateFields.push('email = ?');
      updateValues.push(email);
    }

    if (role && ['customer', 'admin'].includes(role)) {
      updateFields.push('role = ?');
      updateValues.push(role);
    }

    if (updateFields.length === 0) {
      return error(res, '没有提供要更新的信息', 400);
    }

    // 执行更新
    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    updateValues.push(id);

    await db.execute(
      `UPDATE users SET ${updateFields.join(', ')} WHERE id = ?`,
      updateValues
    );

    // 获取更新后的用户信息
    const [updatedUser] = await db.execute(
      'SELECT id, username, email, role, created_at, updated_at FROM users WHERE id = ?',
      [id]
    );

    success(res, { user: updatedUser[0] }, '用户信息更新成功');

  } catch (err) {
    console.error('更新用户信息错误:', err);
    error(res, '更新用户信息失败', 500);
  }
}

/**
 * 重置用户密码
 */
async function resetUserPassword(req, res) {
  try {
    const { id } = req.params;
    const { new_password } = req.body;

    if (!new_password || new_password.length < 6) {
      return error(res, '新密码长度不能少于6位', 400);
    }

    // 检查用户是否存在
    const [users] = await db.execute(
      'SELECT id FROM users WHERE id = ?',
      [id]
    );

    if (users.length === 0) {
      return error(res, '用户不存在', 404);
    }

    // 加密新密码
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(new_password, saltRounds);

    // 更新密码
    await db.execute(
      'UPDATE users SET password_hash = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [passwordHash, id]
    );

    success(res, null, '用户密码重置成功');

  } catch (err) {
    console.error('重置用户密码错误:', err);
    error(res, '重置用户密码失败', 500);
  }
}

/**
 * 删除用户
 */
async function deleteUser(req, res) {
  try {
    const { id } = req.params;

    // 检查用户是否存在
    const [users] = await db.execute(
      'SELECT id, role FROM users WHERE id = ?',
      [id]
    );

    if (users.length === 0) {
      return error(res, '用户不存在', 404);
    }

    // 防止删除管理员账户
    if (users[0].role === 'admin') {
      return error(res, '不能删除管理员账户', 403);
    }

    // 检查用户是否有未完成的订单
    const [pendingOrders] = await db.execute(
      'SELECT COUNT(*) as count FROM orders WHERE user_id = ? AND status = "pending_payment"',
      [id]
    );

    if (pendingOrders[0].count > 0) {
      return error(res, '用户有未完成的订单，无法删除', 400);
    }

    // 开始事务删除用户相关数据
    await db.execute('START TRANSACTION');

    try {
      // 删除购物车
      await db.execute('DELETE FROM shopping_cart_items WHERE user_id = ?', [id]);
      
      // 删除用户（订单保留用于数据分析）
      await db.execute('DELETE FROM users WHERE id = ?', [id]);

      await db.execute('COMMIT');

      success(res, null, '用户删除成功');

    } catch (err) {
      await db.execute('ROLLBACK');
      throw err;
    }

  } catch (err) {
    console.error('删除用户错误:', err);
    error(res, '删除用户失败', 500);
  }
}

module.exports = {
  getUserList,
  getUserDetail,
  createUser,
  updateUser,
  resetUserPassword,
  deleteUser
};
