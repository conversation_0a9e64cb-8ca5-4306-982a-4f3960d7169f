const db = require('../config/database');
const { success, error, notFound } = require('../utils/response');

/**
 * 获取订单列表（管理员视图）
 */
async function getOrderList(req, res) {
  try {
    const {
      page = 1,
      limit = 20,
      status = 'all',
      search = '',
      date_from = '',
      date_to = '',
      sort = 'created_desc'
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);
    
    // 构建查询条件
    let whereClause = 'WHERE 1=1';
    let queryParams = [];

    // 状态筛选
    if (status !== 'all') {
      whereClause += ' AND o.status = ?';
      queryParams.push(status);
    }

    // 搜索条件（订单号、用户名、邮箱）
    if (search) {
      whereClause += ' AND (o.order_number LIKE ? OR u.username LIKE ? OR u.email LIKE ? OR o.receiving_email LIKE ?)';
      queryParams.push(`%${search}%`, `%${search}%`, `%${search}%`, `%${search}%`);
    }

    // 日期范围筛选
    if (date_from) {
      whereClause += ' AND DATE(o.created_at) >= ?';
      queryParams.push(date_from);
    }

    if (date_to) {
      whereClause += ' AND DATE(o.created_at) <= ?';
      queryParams.push(date_to);
    }

    // 排序条件
    let orderClause = 'ORDER BY ';
    switch (sort) {
      case 'created_asc':
        orderClause += 'o.created_at ASC';
        break;
      case 'created_desc':
        orderClause += 'o.created_at DESC';
        break;
      case 'amount_asc':
        orderClause += 'o.total_amount ASC';
        break;
      case 'amount_desc':
        orderClause += 'o.total_amount DESC';
        break;
      case 'payment_time_desc':
        orderClause += 'o.payment_time DESC';
        break;
      default:
        orderClause += 'o.created_at DESC';
    }

    // 查询订单列表
    const [orders] = await db.execute(
      `SELECT 
         o.id,
         o.order_number,
         o.user_id,
         o.total_amount,
         o.receiving_email,
         o.status,
         o.payment_method,
         o.payment_time,
         o.created_at,
         o.updated_at,
         u.username,
         u.email as user_email,
         COUNT(oi.id) as item_count
       FROM orders o
       LEFT JOIN users u ON o.user_id = u.id
       LEFT JOIN order_items oi ON o.id = oi.order_id
       ${whereClause}
       GROUP BY o.id
       ${orderClause}
       LIMIT ? OFFSET ?`,
      [...queryParams, parseInt(limit), offset]
    );

    // 查询总数
    const [countResult] = await db.execute(
      `SELECT COUNT(DISTINCT o.id) as total 
       FROM orders o
       LEFT JOIN users u ON o.user_id = u.id
       ${whereClause}`,
      queryParams
    );

    const total = countResult[0].total;
    const totalPages = Math.ceil(total / parseInt(limit));

    // 格式化订单数据
    const formattedOrders = orders.map(order => ({
      id: order.id,
      order_number: order.order_number,
      user_info: {
        user_id: order.user_id,
        username: order.username,
        email: order.user_email
      },
      total_amount: parseFloat(order.total_amount),
      receiving_email: order.receiving_email,
      status: order.status,
      payment_method: order.payment_method,
      payment_time: order.payment_time,
      item_count: order.item_count,
      created_at: order.created_at,
      updated_at: order.updated_at
    }));

    success(res, '获取订单列表成功', {
      orders: formattedOrders,
      pagination: {
        current_page: parseInt(page),
        per_page: parseInt(limit),
        total: total,
        total_pages: totalPages,
        has_next: parseInt(page) < totalPages,
        has_prev: parseInt(page) > 1
      }
    });

  } catch (err) {
    console.error('获取订单列表错误:', err);
    error(res, '获取订单列表失败', 500);
  }
}

/**
 * 获取订单详情（管理员视图）
 */
async function getOrderDetail(req, res) {
  try {
    const { id } = req.params;

    // 查询订单基本信息
    const [orders] = await db.execute(
      `SELECT 
         o.*,
         u.username,
         u.email as user_email,
         u.created_at as user_created_at
       FROM orders o
       LEFT JOIN users u ON o.user_id = u.id
       WHERE o.id = ?`,
      [id]
    );

    if (orders.length === 0) {
      return notFound(res, '订单不存在');
    }

    const order = orders[0];

    // 查询订单商品详情
    const [orderItems] = await db.execute(
      `SELECT 
         oi.*,
         p.name as product_name,
         p.school_logo_url,
         pi.email_account,
         pi.email_password,
         pi.status as inventory_status
       FROM order_items oi
       LEFT JOIN products p ON oi.product_id = p.id
       LEFT JOIN product_inventory pi ON oi.inventory_id = pi.id
       WHERE oi.order_id = ?`,
      [id]
    );

    const orderDetail = {
      id: order.id,
      order_number: order.order_number,
      user_info: {
        user_id: order.user_id,
        username: order.username,
        email: order.user_email,
        created_at: order.user_created_at
      },
      total_amount: parseFloat(order.total_amount),
      receiving_email: order.receiving_email,
      status: order.status,
      payment_method: order.payment_method,
      payment_time: order.payment_time,
      created_at: order.created_at,
      updated_at: order.updated_at,
      items: orderItems.map(item => ({
        id: item.id,
        product_id: item.product_id,
        product_name: item.product_name,
        school_logo_url: item.school_logo_url,
        price: parseFloat(item.price),
        quantity: item.quantity,
        total_price: parseFloat(item.price) * item.quantity,
        inventory_info: item.inventory_id ? {
          inventory_id: item.inventory_id,
          email_account: item.email_account,
          email_password: item.email_password,
          status: item.inventory_status
        } : null
      }))
    };

    success(res, { order: orderDetail }, '获取订单详情成功');

  } catch (err) {
    console.error('获取订单详情错误:', err);
    error(res, '获取订单详情失败', 500);
  }
}

/**
 * 更新订单状态
 */
async function updateOrderStatus(req, res) {
  try {
    const { id } = req.params;
    const { status, reason = '' } = req.body;

    if (!['pending_payment', 'completed', 'cancelled'].includes(status)) {
      return error(res, '订单状态无效', 400);
    }

    // 检查订单是否存在
    const [orders] = await db.execute(
      'SELECT id, status, order_number FROM orders WHERE id = ?',
      [id]
    );

    if (orders.length === 0) {
      return notFound(res, '订单不存在');
    }

    const currentOrder = orders[0];

    // 如果状态没有变化，直接返回
    if (currentOrder.status === status) {
      return success(res, '订单状态未发生变化', null);
    }

    // 开始事务处理状态变更
    await db.execute('START TRANSACTION');

    try {
      // 更新订单状态
      await db.execute(
        'UPDATE orders SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [status, id]
      );

      // 如果从已完成改为其他状态，需要释放库存
      if (currentOrder.status === 'completed' && status !== 'completed') {
        await db.execute(
          `UPDATE product_inventory pi
           JOIN order_items oi ON pi.id = oi.inventory_id
           SET pi.status = 'available'
           WHERE oi.order_id = ? AND pi.status = 'sold'`,
          [id]
        );

        // 清除订单项的库存分配
        await db.execute(
          'UPDATE order_items SET inventory_id = NULL WHERE order_id = ?',
          [id]
        );
      }

      // 如果改为已完成状态，需要分配库存
      if (status === 'completed' && currentOrder.status !== 'completed') {
        // 获取订单商品
        const [orderItems] = await db.execute(
          'SELECT id, product_id, quantity FROM order_items WHERE order_id = ?',
          [id]
        );

        for (const item of orderItems) {
          // 为每个商品分配库存
          const [availableInventory] = await db.execute(
            'SELECT id FROM product_inventory WHERE product_id = ? AND status = "available" LIMIT ?',
            [item.product_id, item.quantity]
          );

          if (availableInventory.length < item.quantity) {
            await db.execute('ROLLBACK');
            return error(res, `商品库存不足，无法完成订单`, 400);
          }

          // 分配库存
          for (let i = 0; i < item.quantity; i++) {
            const inventoryId = availableInventory[i].id;
            
            // 更新库存状态
            await db.execute(
              'UPDATE product_inventory SET status = "sold", updated_at = CURRENT_TIMESTAMP WHERE id = ?',
              [inventoryId]
            );

            // 关联到订单项（简化处理，只关联第一个）
            if (i === 0) {
              await db.execute(
                'UPDATE order_items SET inventory_id = ? WHERE id = ?',
                [inventoryId, item.id]
              );
            }
          }
        }

        // 设置支付时间
        await db.execute(
          'UPDATE orders SET payment_time = CURRENT_TIMESTAMP WHERE id = ?',
          [id]
        );
      }

      await db.execute('COMMIT');

      success(res, {
        order_id: id,
        order_number: currentOrder.order_number,
        old_status: currentOrder.status,
        new_status: status,
        reason: reason
      }, '订单状态更新成功');

    } catch (err) {
      await db.execute('ROLLBACK');
      throw err;
    }

  } catch (err) {
    console.error('更新订单状态错误:', err);
    error(res, '更新订单状态失败', 500);
  }
}

/**
 * 手动发货
 */
async function manualFulfillOrder(req, res) {
  try {
    const { id } = req.params;
    const { inventory_assignments } = req.body;

    // 检查订单是否存在且状态正确
    const [orders] = await db.execute(
      'SELECT id, status, order_number FROM orders WHERE id = ?',
      [id]
    );

    if (orders.length === 0) {
      return notFound(res, '订单不存在');
    }

    if (orders[0].status !== 'pending_payment') {
      return error(res, '只能对待支付订单进行手动发货', 400);
    }

    if (!Array.isArray(inventory_assignments) || inventory_assignments.length === 0) {
      return error(res, '请提供库存分配信息', 400);
    }

    // 开始事务处理手动发货
    await db.execute('START TRANSACTION');

    try {
      // 验证并分配库存
      for (const assignment of inventory_assignments) {
        const { order_item_id, inventory_id } = assignment;

        // 检查订单项是否存在
        const [orderItems] = await db.execute(
          'SELECT id, product_id FROM order_items WHERE id = ? AND order_id = ?',
          [order_item_id, id]
        );

        if (orderItems.length === 0) {
          await db.execute('ROLLBACK');
          return error(res, `订单项 ${order_item_id} 不存在`, 400);
        }

        // 检查库存是否可用
        const [inventory] = await db.execute(
          'SELECT id, product_id, status FROM product_inventory WHERE id = ?',
          [inventory_id]
        );

        if (inventory.length === 0) {
          await db.execute('ROLLBACK');
          return error(res, `库存 ${inventory_id} 不存在`, 400);
        }

        if (inventory[0].status !== 'available') {
          await db.execute('ROLLBACK');
          return error(res, `库存 ${inventory_id} 不可用`, 400);
        }

        if (inventory[0].product_id !== orderItems[0].product_id) {
          await db.execute('ROLLBACK');
          return error(res, `库存 ${inventory_id} 与订单项商品不匹配`, 400);
        }

        // 分配库存
        await db.execute(
          'UPDATE product_inventory SET status = "sold", updated_at = CURRENT_TIMESTAMP WHERE id = ?',
          [inventory_id]
        );

        await db.execute(
          'UPDATE order_items SET inventory_id = ? WHERE id = ?',
          [inventory_id, order_item_id]
        );
      }

      // 更新订单状态为已完成
      await db.execute(
        'UPDATE orders SET status = "completed", payment_time = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [id]
      );

      await db.execute('COMMIT');

      success(res, {
        order_id: id,
        order_number: orders[0].order_number,
        assigned_count: inventory_assignments.length
      }, '手动发货成功');

    } catch (err) {
      await db.execute('ROLLBACK');
      throw err;
    }

  } catch (err) {
    console.error('手动发货错误:', err);
    error(res, '手动发货失败', 500);
  }
}

/**
 * 处理退款
 */
async function processRefund(req, res) {
  try {
    const { id } = req.params;
    const { reason = '', refund_amount } = req.body;

    // 检查订单是否存在
    const [orders] = await db.execute(
      'SELECT id, status, total_amount, order_number FROM orders WHERE id = ?',
      [id]
    );

    if (orders.length === 0) {
      return notFound(res, '订单不存在');
    }

    const order = orders[0];

    if (order.status !== 'completed') {
      return error(res, '只能对已完成的订单进行退款', 400);
    }

    const refundAmount = refund_amount || order.total_amount;

    if (parseFloat(refundAmount) <= 0 || parseFloat(refundAmount) > parseFloat(order.total_amount)) {
      return error(res, '退款金额无效', 400);
    }

    // 开始事务处理退款
    await db.execute('START TRANSACTION');

    try {
      // 释放已分配的库存
      await db.execute(
        `UPDATE product_inventory pi
         JOIN order_items oi ON pi.id = oi.inventory_id
         SET pi.status = 'available', pi.updated_at = CURRENT_TIMESTAMP
         WHERE oi.order_id = ? AND pi.status = 'sold'`,
        [id]
      );

      // 清除订单项的库存分配
      await db.execute(
        'UPDATE order_items SET inventory_id = NULL WHERE order_id = ?',
        [id]
      );

      // 更新订单状态为已取消
      await db.execute(
        'UPDATE orders SET status = "cancelled", updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [id]
      );

      await db.execute('COMMIT');

      success(res, {
        order_id: id,
        order_number: order.order_number,
        refund_amount: parseFloat(refundAmount),
        reason: reason
      }, '退款处理成功');

    } catch (err) {
      await db.execute('ROLLBACK');
      throw err;
    }

  } catch (err) {
    console.error('处理退款错误:', err);
    error(res, '处理退款失败', 500);
  }
}

/**
 * 获取订单统计
 */
async function getOrderStats(req, res) {
  try {
    const { date_from = '', date_to = '' } = req.query;

    let whereClause = 'WHERE 1=1';
    let queryParams = [];

    if (date_from) {
      whereClause += ' AND DATE(created_at) >= ?';
      queryParams.push(date_from);
    }

    if (date_to) {
      whereClause += ' AND DATE(created_at) <= ?';
      queryParams.push(date_to);
    }

    // 查询订单统计
    const [stats] = await db.execute(
      `SELECT 
         COUNT(*) as total_orders,
         COUNT(CASE WHEN status = 'pending_payment' THEN 1 END) as pending_orders,
         COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_orders,
         COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_orders,
         COALESCE(SUM(CASE WHEN status = 'completed' THEN total_amount ELSE 0 END), 0) as total_revenue,
         COALESCE(AVG(CASE WHEN status = 'completed' THEN total_amount END), 0) as avg_order_value
       FROM orders 
       ${whereClause}`,
      queryParams
    );

    // 查询支付方式分布
    const [paymentStats] = await db.execute(
      `SELECT 
         payment_method,
         COUNT(*) as count,
         COALESCE(SUM(total_amount), 0) as amount
       FROM orders 
       ${whereClause} AND status = 'completed'
       GROUP BY payment_method`,
      queryParams
    );

    const orderStats = {
      overview: {
        total_orders: stats[0].total_orders,
        pending_orders: stats[0].pending_orders,
        completed_orders: stats[0].completed_orders,
        cancelled_orders: stats[0].cancelled_orders,
        total_revenue: parseFloat(stats[0].total_revenue),
        avg_order_value: parseFloat(stats[0].avg_order_value)
      },
      payment_methods: paymentStats.map(item => ({
        method: item.payment_method,
        count: item.count,
        amount: parseFloat(item.amount)
      }))
    };

    success(res, '获取订单统计成功', orderStats);

  } catch (err) {
    console.error('获取订单统计错误:', err);
    error(res, '获取订单统计失败', 500);
  }
}

module.exports = {
  getOrderList,
  getOrderDetail,
  updateOrderStatus,
  manualFulfillOrder,
  processRefund,
  getOrderStats
};
