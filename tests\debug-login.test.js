const request = require('supertest');
const express = require('express');
const cors = require('cors');
const { setupTestDatabase, cleanupTestDatabase } = require('./setup');

// 创建测试应用
const app = express();
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 导入路由
const adminRoutes = require('../src/routes/admin');
app.use('/api/admin', adminRoutes);

describe('调试登录测试', () => {
  beforeAll(async () => {
    await setupTestDatabase();
  });

  afterAll(async () => {
    await cleanupTestDatabase();
  });

  test('调试管理员登录响应', async () => {
    const response = await request(app)
      .post('/api/admin/auth/login')
      .send({
        username: 'testadmin',
        password: 'admin123'
      });

    console.log('Response status:', response.status);
    console.log('Response body:', JSON.stringify(response.body, null, 2));
    
    // 基本检查
    expect(response.status).toBeDefined();
    expect(response.body).toBeDefined();
  });
});
