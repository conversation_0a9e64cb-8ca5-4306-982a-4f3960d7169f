const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

async function initDatabase() {
  try {
    // 创建数据库连接（不指定数据库）
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      charset: 'utf8mb4'
    });

    console.log('连接到MySQL服务器成功');

    // 创建数据库
    await connection.execute('CREATE DATABASE IF NOT EXISTS edu_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci');
    console.log('数据库 edu_db 创建成功');

    // 关闭连接并重新连接到指定数据库
    await connection.end();

    const dbConnection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'root',
      database: 'edu_db',
      charset: 'utf8mb4'
    });

    // 读取并执行主数据库脚本
    const mainSqlPath = path.join(__dirname, 'edushop.sql');
    if (fs.existsSync(mainSqlPath)) {
      const mainSql = fs.readFileSync(mainSqlPath, 'utf8');
      const statements = mainSql.split(';').filter(stmt => stmt.trim());
      
      for (const statement of statements) {
        if (statement.trim()) {
          await dbConnection.execute(statement);
        }
      }
      console.log('主数据库表创建成功');
    }

    // 读取并执行管理员表脚本
    const adminSqlPath = path.join(__dirname, 'admin_tables.sql');
    if (fs.existsSync(adminSqlPath)) {
      const adminSql = fs.readFileSync(adminSqlPath, 'utf8');
      const statements = adminSql.split(';').filter(stmt => stmt.trim());
      
      for (const statement of statements) {
        if (statement.trim()) {
          await dbConnection.execute(statement);
        }
      }
      console.log('管理员相关表创建成功');
    }

    await dbConnection.end();
    console.log('数据库初始化完成！');

  } catch (error) {
    console.error('数据库初始化失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  require('dotenv').config();
  initDatabase();
}

module.exports = { initDatabase };
