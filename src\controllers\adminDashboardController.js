const db = require('../config/database');
const { success, error } = require('../utils/response');

/**
 * 获取仪表盘概览数据
 */
async function getDashboardOverview(req, res) {
  try {
    // 获取今日日期
    const today = new Date().toISOString().split('T')[0];
    const thisMonth = new Date().toISOString().slice(0, 7);

    // 并行查询各种统计数据
    const [
      todayOrdersResult,
      todaySalesResult,
      totalUsersResult,
      totalOrdersResult,
      totalSalesResult,
      pendingOrdersResult,
      lowStockProductsResult,
      thisMonthOrdersResult,
      thisMonthSalesResult,
      recentOrdersResult
    ] = await Promise.all([
      // 今日订单数
      db.execute(
        'SELECT COUNT(*) as count FROM orders WHERE DATE(created_at) = ?',
        [today]
      ),
      // 今日销售额
      db.execute(
        'SELECT COALESCE(SUM(total_amount), 0) as amount FROM orders WHERE DATE(created_at) = ? AND status = "completed"',
        [today]
      ),
      // 总用户数
      db.execute(
        'SELECT COUNT(*) as count FROM users WHERE role = "customer"'
      ),
      // 总订单数
      db.execute(
        'SELECT COUNT(*) as count FROM orders'
      ),
      // 总销售额
      db.execute(
        'SELECT COALESCE(SUM(total_amount), 0) as amount FROM orders WHERE status = "completed"'
      ),
      // 待处理订单数
      db.execute(
        'SELECT COUNT(*) as count FROM orders WHERE status = "pending_payment"'
      ),
      // 库存预警商品数
      db.execute(
        `SELECT COUNT(DISTINCT p.id) as count 
         FROM products p 
         LEFT JOIN product_inventory pi ON p.id = pi.product_id AND pi.status = 'available'
         GROUP BY p.id
         HAVING COUNT(pi.id) < 5`
      ),
      // 本月订单数
      db.execute(
        'SELECT COUNT(*) as count FROM orders WHERE DATE_FORMAT(created_at, "%Y-%m") = ?',
        [thisMonth]
      ),
      // 本月销售额
      db.execute(
        'SELECT COALESCE(SUM(total_amount), 0) as amount FROM orders WHERE DATE_FORMAT(created_at, "%Y-%m") = ? AND status = "completed"',
        [thisMonth]
      ),
      // 最近订单
      db.execute(
        `SELECT o.id, o.order_number, o.total_amount, o.status, o.created_at, u.username
         FROM orders o
         LEFT JOIN users u ON o.user_id = u.id
         ORDER BY o.created_at DESC
         LIMIT 10`
      )
    ]);

    // 组装数据
    const overview = {
      today: {
        orders: todayOrdersResult[0][0].count,
        sales: parseFloat(todaySalesResult[0][0].amount)
      },
      total: {
        users: totalUsersResult[0][0].count,
        orders: totalOrdersResult[0][0].count,
        sales: parseFloat(totalSalesResult[0][0].amount)
      },
      pending: {
        orders: pendingOrdersResult[0][0].count,
        low_stock_products: lowStockProductsResult[0].length > 0 ? lowStockProductsResult[0][0].count : 0
      },
      this_month: {
        orders: thisMonthOrdersResult[0][0].count,
        sales: parseFloat(thisMonthSalesResult[0][0].amount)
      },
      recent_orders: recentOrdersResult[0]
    };

    success(res, '获取仪表盘数据成功', overview);

  } catch (err) {
    console.error('获取仪表盘数据错误:', err);
    error(res, '获取仪表盘数据失败', 500);
  }
}

/**
 * 获取销售趋势图表数据
 */
async function getSalesChart(req, res) {
  try {
    const { period = '7days' } = req.query;

    let dateFormat, dateRange;
    
    switch (period) {
      case '7days':
        dateFormat = '%Y-%m-%d';
        dateRange = 'DATE_SUB(CURDATE(), INTERVAL 6 DAY)';
        break;
      case '30days':
        dateFormat = '%Y-%m-%d';
        dateRange = 'DATE_SUB(CURDATE(), INTERVAL 29 DAY)';
        break;
      case '12months':
        dateFormat = '%Y-%m';
        dateRange = 'DATE_SUB(CURDATE(), INTERVAL 11 MONTH)';
        break;
      default:
        dateFormat = '%Y-%m-%d';
        dateRange = 'DATE_SUB(CURDATE(), INTERVAL 6 DAY)';
    }

    // 查询销售趋势数据
    const [salesData] = await db.execute(
      `SELECT 
         DATE_FORMAT(created_at, ?) as date,
         COUNT(*) as order_count,
         COALESCE(SUM(CASE WHEN status = 'completed' THEN total_amount ELSE 0 END), 0) as sales_amount
       FROM orders 
       WHERE created_at >= ${dateRange}
       GROUP BY DATE_FORMAT(created_at, ?)
       ORDER BY date ASC`,
      [dateFormat, dateFormat]
    );

    // 查询订单状态分布
    const [statusData] = await db.execute(
      `SELECT 
         status,
         COUNT(*) as count
       FROM orders 
       WHERE created_at >= ${dateRange}
       GROUP BY status`
    );

    const chartData = {
      sales_trend: salesData.map(item => ({
        date: item.date,
        orders: item.order_count,
        sales: parseFloat(item.sales_amount)
      })),
      order_status: statusData.map(item => ({
        status: item.status,
        count: item.count
      }))
    };

    success(res, '获取图表数据成功', chartData);

  } catch (err) {
    console.error('获取图表数据错误:', err);
    error(res, '获取图表数据失败', 500);
  }
}

/**
 * 获取商品销售排行
 */
async function getProductRanking(req, res) {
  try {
    const { limit = 10 } = req.query;

    // 查询商品销售排行
    const [rankingData] = await db.execute(
      `SELECT 
         p.id,
         p.name,
         p.price,
         COUNT(oi.id) as sales_count,
         SUM(oi.price * oi.quantity) as sales_amount,
         (SELECT COUNT(*) FROM product_inventory WHERE product_id = p.id AND status = 'available') as available_stock
       FROM products p
       LEFT JOIN order_items oi ON p.id = oi.product_id
       LEFT JOIN orders o ON oi.order_id = o.id AND o.status = 'completed'
       WHERE p.status = 'active'
       GROUP BY p.id, p.name, p.price
       ORDER BY sales_count DESC, sales_amount DESC
       LIMIT ?`,
      [parseInt(limit)]
    );

    const ranking = rankingData.map(item => ({
      id: item.id,
      name: item.name,
      price: parseFloat(item.price),
      sales_count: item.sales_count,
      sales_amount: parseFloat(item.sales_amount || 0),
      available_stock: item.available_stock
    }));

    success(res, { ranking }, '获取商品销售排行成功');

  } catch (err) {
    console.error('获取商品销售排行错误:', err);
    error(res, '获取商品销售排行失败', 500);
  }
}

/**
 * 获取库存预警信息
 */
async function getInventoryAlerts(req, res) {
  try {
    // 从系统配置获取预警阈值
    const [thresholdResult] = await db.execute(
      'SELECT value FROM system_settings WHERE `key` = "inventory_low_stock_threshold"'
    );
    
    const threshold = thresholdResult.length > 0 ? parseInt(thresholdResult[0].value) : 5;

    // 查询库存预警商品
    const [alertsData] = await db.execute(
      `SELECT 
         p.id,
         p.name,
         p.price,
         p.status,
         COUNT(CASE WHEN pi.status = 'available' THEN 1 END) as available_count,
         COUNT(CASE WHEN pi.status = 'sold' THEN 1 END) as sold_count,
         COUNT(CASE WHEN pi.status = 'reserved' THEN 1 END) as reserved_count,
         COUNT(pi.id) as total_count
       FROM products p
       LEFT JOIN product_inventory pi ON p.id = pi.product_id
       WHERE p.status = 'active'
       GROUP BY p.id, p.name, p.price, p.status
       HAVING available_count < ?
       ORDER BY available_count ASC`,
      [threshold]
    );

    const alerts = alertsData.map(item => ({
      product_id: item.id,
      product_name: item.name,
      price: parseFloat(item.price),
      available_count: item.available_count,
      sold_count: item.sold_count,
      reserved_count: item.reserved_count,
      total_count: item.total_count,
      alert_level: item.available_count === 0 ? 'critical' : 
                   item.available_count <= Math.floor(threshold / 2) ? 'high' : 'medium'
    }));

    success(res, { 
      threshold,
      alerts,
      total_alerts: alerts.length
    }, '获取库存预警成功');

  } catch (err) {
    console.error('获取库存预警错误:', err);
    error(res, '获取库存预警失败', 500);
  }
}

/**
 * 获取用户统计信息
 */
async function getUserStats(req, res) {
  try {
    const today = new Date().toISOString().split('T')[0];
    const thisMonth = new Date().toISOString().slice(0, 7);

    // 并行查询用户统计数据
    const [
      totalUsersResult,
      todayRegistersResult,
      monthRegistersResult,
      activeUsersResult,
      topCustomersResult
    ] = await Promise.all([
      // 总用户数
      db.execute(
        'SELECT COUNT(*) as count FROM users WHERE role = "customer"'
      ),
      // 今日注册用户
      db.execute(
        'SELECT COUNT(*) as count FROM users WHERE role = "customer" AND DATE(created_at) = ?',
        [today]
      ),
      // 本月注册用户
      db.execute(
        'SELECT COUNT(*) as count FROM users WHERE role = "customer" AND DATE_FORMAT(created_at, "%Y-%m") = ?',
        [thisMonth]
      ),
      // 活跃用户（有订单的用户）
      db.execute(
        'SELECT COUNT(DISTINCT user_id) as count FROM orders WHERE status = "completed"'
      ),
      // 消费排行榜
      db.execute(
        `SELECT 
           u.id, u.username, u.email,
           COUNT(o.id) as order_count,
           COALESCE(SUM(o.total_amount), 0) as total_spent
         FROM users u
         LEFT JOIN orders o ON u.id = o.user_id AND o.status = 'completed'
         WHERE u.role = 'customer'
         GROUP BY u.id, u.username, u.email
         HAVING total_spent > 0
         ORDER BY total_spent DESC
         LIMIT 10`
      )
    ]);

    const userStats = {
      total_users: totalUsersResult[0][0].count,
      today_registers: todayRegistersResult[0][0].count,
      month_registers: monthRegistersResult[0][0].count,
      active_users: activeUsersResult[0][0].count,
      top_customers: topCustomersResult[0].map(customer => ({
        id: customer.id,
        username: customer.username,
        email: customer.email,
        order_count: customer.order_count,
        total_spent: parseFloat(customer.total_spent)
      }))
    };

    success(res, '获取用户统计成功', userStats);

  } catch (err) {
    console.error('获取用户统计错误:', err);
    error(res, '获取用户统计失败', 500);
  }
}

module.exports = {
  getDashboardOverview,
  getSalesChart,
  getProductRanking,
  getInventoryAlerts,
  getUserStats
};
