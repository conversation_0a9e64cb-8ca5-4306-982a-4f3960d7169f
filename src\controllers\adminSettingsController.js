const db = require('../config/database');
const { success, error } = require('../utils/response');

/**
 * 获取所有系统配置
 */
async function getAllSettings(req, res) {
  try {
    const [settings] = await db.execute(
      'SELECT `key`, `value`, `type`, `description` FROM system_settings ORDER BY `key`'
    );

    // 按类别组织配置
    const organizedSettings = {
      site: {},
      payment: {},
      email: {},
      system: {}
    };

    settings.forEach(setting => {
      const { key, value, type, description } = setting;
      
      // 转换值类型
      let convertedValue = value;
      switch (type) {
        case 'number':
          convertedValue = parseFloat(value);
          break;
        case 'boolean':
          convertedValue = value === 'true';
          break;
        case 'json':
          try {
            convertedValue = JSON.parse(value);
          } catch (e) {
            convertedValue = value;
          }
          break;
      }

      // 按前缀分类
      if (key.startsWith('site_')) {
        organizedSettings.site[key] = { value: convertedValue, type, description };
      } else if (key.startsWith('payment_')) {
        organizedSettings.payment[key] = { value: convertedValue, type, description };
      } else if (key.startsWith('email_')) {
        organizedSettings.email[key] = { value: convertedValue, type, description };
      } else {
        organizedSettings.system[key] = { value: convertedValue, type, description };
      }
    });

    success(res, { settings: organizedSettings }, '获取系统配置成功');

  } catch (err) {
    console.error('获取系统配置错误:', err);
    error(res, '获取系统配置失败', 500);
  }
}

/**
 * 获取网站配置
 */
async function getSiteSettings(req, res) {
  try {
    const [settings] = await db.execute(
      'SELECT `key`, `value`, `type`, `description` FROM system_settings WHERE `key` LIKE "site_%"'
    );

    const siteSettings = {};
    settings.forEach(setting => {
      const { key, value, type } = setting;
      
      let convertedValue = value;
      switch (type) {
        case 'number':
          convertedValue = parseFloat(value);
          break;
        case 'boolean':
          convertedValue = value === 'true';
          break;
        case 'json':
          try {
            convertedValue = JSON.parse(value);
          } catch (e) {
            convertedValue = value;
          }
          break;
      }
      
      siteSettings[key] = convertedValue;
    });

    success(res, { settings: siteSettings }, '获取网站配置成功');

  } catch (err) {
    console.error('获取网站配置错误:', err);
    error(res, '获取网站配置失败', 500);
  }
}

/**
 * 更新网站配置
 */
async function updateSiteSettings(req, res) {
  try {
    const updates = req.body;

    if (!updates || Object.keys(updates).length === 0) {
      return error(res, '没有提供要更新的配置', 400);
    }

    // 验证配置键是否都是网站配置
    const validSiteKeys = Object.keys(updates).filter(key => key.startsWith('site_'));
    if (validSiteKeys.length !== Object.keys(updates).length) {
      return error(res, '只能更新网站相关配置', 400);
    }

    // 开始事务更新配置
    await db.execute('START TRANSACTION');

    try {
      for (const [key, value] of Object.entries(updates)) {
        // 检查配置是否存在
        const [existing] = await db.execute(
          'SELECT `key`, `type` FROM system_settings WHERE `key` = ?',
          [key]
        );

        if (existing.length === 0) {
          // 如果配置不存在，创建新配置
          await db.execute(
            'INSERT INTO system_settings (`key`, `value`, `type`) VALUES (?, ?, ?)',
            [key, String(value), typeof value === 'number' ? 'number' : typeof value === 'boolean' ? 'boolean' : 'string']
          );
        } else {
          // 更新现有配置
          await db.execute(
            'UPDATE system_settings SET `value` = ?, updated_at = CURRENT_TIMESTAMP WHERE `key` = ?',
            [String(value), key]
          );
        }
      }

      await db.execute('COMMIT');

      success(res, { updated_keys: Object.keys(updates) }, '网站配置更新成功');

    } catch (err) {
      await db.execute('ROLLBACK');
      throw err;
    }

  } catch (err) {
    console.error('更新网站配置错误:', err);
    error(res, '更新网站配置失败', 500);
  }
}

/**
 * 获取支付配置
 */
async function getPaymentSettings(req, res) {
  try {
    const [settings] = await db.execute(
      'SELECT `key`, `value`, `type`, `description` FROM system_settings WHERE `key` LIKE "payment_%"'
    );

    const paymentSettings = {};
    settings.forEach(setting => {
      const { key, value, type } = setting;
      
      let convertedValue = value;
      switch (type) {
        case 'number':
          convertedValue = parseFloat(value);
          break;
        case 'boolean':
          convertedValue = value === 'true';
          break;
        case 'json':
          try {
            convertedValue = JSON.parse(value);
          } catch (e) {
            convertedValue = value;
          }
          break;
      }
      
      paymentSettings[key] = convertedValue;
    });

    success(res, { settings: paymentSettings }, '获取支付配置成功');

  } catch (err) {
    console.error('获取支付配置错误:', err);
    error(res, '获取支付配置失败', 500);
  }
}

/**
 * 更新支付配置
 */
async function updatePaymentSettings(req, res) {
  try {
    const updates = req.body;

    if (!updates || Object.keys(updates).length === 0) {
      return error(res, '没有提供要更新的配置', 400);
    }

    // 验证配置键是否都是支付配置
    const validPaymentKeys = Object.keys(updates).filter(key => key.startsWith('payment_'));
    if (validPaymentKeys.length !== Object.keys(updates).length) {
      return error(res, '只能更新支付相关配置', 400);
    }

    // 开始事务更新配置
    await db.execute('START TRANSACTION');

    try {
      for (const [key, value] of Object.entries(updates)) {
        // 检查配置是否存在
        const [existing] = await db.execute(
          'SELECT `key`, `type` FROM system_settings WHERE `key` = ?',
          [key]
        );

        if (existing.length === 0) {
          // 如果配置不存在，创建新配置
          await db.execute(
            'INSERT INTO system_settings (`key`, `value`, `type`) VALUES (?, ?, ?)',
            [key, String(value), typeof value === 'number' ? 'number' : typeof value === 'boolean' ? 'boolean' : 'string']
          );
        } else {
          // 更新现有配置
          await db.execute(
            'UPDATE system_settings SET `value` = ?, updated_at = CURRENT_TIMESTAMP WHERE `key` = ?',
            [String(value), key]
          );
        }
      }

      await db.execute('COMMIT');

      success(res, { updated_keys: Object.keys(updates) }, '支付配置更新成功');

    } catch (err) {
      await db.execute('ROLLBACK');
      throw err;
    }

  } catch (err) {
    console.error('更新支付配置错误:', err);
    error(res, '更新支付配置失败', 500);
  }
}

/**
 * 获取邮件配置
 */
async function getEmailSettings(req, res) {
  try {
    const [settings] = await db.execute(
      'SELECT `key`, `value`, `type`, `description` FROM system_settings WHERE `key` LIKE "email_%"'
    );

    const emailSettings = {};
    settings.forEach(setting => {
      const { key, value, type } = setting;
      
      let convertedValue = value;
      switch (type) {
        case 'number':
          convertedValue = parseFloat(value);
          break;
        case 'boolean':
          convertedValue = value === 'true';
          break;
        case 'json':
          try {
            convertedValue = JSON.parse(value);
          } catch (e) {
            convertedValue = value;
          }
          break;
      }
      
      // 隐藏敏感信息
      if (key === 'email_smtp_password' && convertedValue) {
        convertedValue = '******';
      }
      
      emailSettings[key] = convertedValue;
    });

    success(res, { settings: emailSettings }, '获取邮件配置成功');

  } catch (err) {
    console.error('获取邮件配置错误:', err);
    error(res, '获取邮件配置失败', 500);
  }
}

/**
 * 更新邮件配置
 */
async function updateEmailSettings(req, res) {
  try {
    const updates = req.body;

    if (!updates || Object.keys(updates).length === 0) {
      return error(res, '没有提供要更新的配置', 400);
    }

    // 验证配置键是否都是邮件配置
    const validEmailKeys = Object.keys(updates).filter(key => key.startsWith('email_'));
    if (validEmailKeys.length !== Object.keys(updates).length) {
      return error(res, '只能更新邮件相关配置', 400);
    }

    // 开始事务更新配置
    await db.execute('START TRANSACTION');

    try {
      for (const [key, value] of Object.entries(updates)) {
        // 跳过空密码更新
        if (key === 'email_smtp_password' && value === '******') {
          continue;
        }

        // 检查配置是否存在
        const [existing] = await db.execute(
          'SELECT `key`, `type` FROM system_settings WHERE `key` = ?',
          [key]
        );

        if (existing.length === 0) {
          // 如果配置不存在，创建新配置
          await db.execute(
            'INSERT INTO system_settings (`key`, `value`, `type`) VALUES (?, ?, ?)',
            [key, String(value), typeof value === 'number' ? 'number' : typeof value === 'boolean' ? 'boolean' : 'string']
          );
        } else {
          // 更新现有配置
          await db.execute(
            'UPDATE system_settings SET `value` = ?, updated_at = CURRENT_TIMESTAMP WHERE `key` = ?',
            [String(value), key]
          );
        }
      }

      await db.execute('COMMIT');

      success(res, { updated_keys: Object.keys(updates) }, '邮件配置更新成功');

    } catch (err) {
      await db.execute('ROLLBACK');
      throw err;
    }

  } catch (err) {
    console.error('更新邮件配置错误:', err);
    error(res, '更新邮件配置失败', 500);
  }
}

/**
 * 测试邮件配置
 */
async function testEmailSettings(req, res) {
  try {
    const { test_email } = req.body;

    if (!test_email) {
      return error(res, '请提供测试邮箱地址', 400);
    }

    // 这里应该实现实际的邮件发送测试
    // 由于这是演示代码，我们只是模拟测试
    
    // 获取邮件配置
    const [settings] = await db.execute(
      'SELECT `key`, `value` FROM system_settings WHERE `key` LIKE "email_%"'
    );

    const emailConfig = {};
    settings.forEach(setting => {
      emailConfig[setting.key] = setting.value;
    });

    // 检查必要的配置是否存在
    const requiredKeys = ['email_smtp_host', 'email_smtp_port', 'email_smtp_user', 'email_smtp_password'];
    const missingKeys = requiredKeys.filter(key => !emailConfig[key]);

    if (missingKeys.length > 0) {
      return error(res, `邮件配置不完整，缺少: ${missingKeys.join(', ')}`, 400);
    }

    // 模拟邮件发送测试
    // 实际实现中应该使用 nodemailer 等库发送测试邮件
    
    success(res, {
      test_email: test_email,
      smtp_host: emailConfig.email_smtp_host,
      smtp_port: emailConfig.email_smtp_port,
      message: '测试邮件发送成功'
    }, '邮件配置测试成功');

  } catch (err) {
    console.error('测试邮件配置错误:', err);
    error(res, '邮件配置测试失败', 500);
  }
}

/**
 * 重置配置到默认值
 */
async function resetSettings(req, res) {
  try {
    const { category } = req.body;

    if (!['site', 'payment', 'email'].includes(category)) {
      return error(res, '配置类别无效', 400);
    }

    // 定义默认配置
    const defaultSettings = {
      site: {
        site_name: 'EDU邮箱售卖平台',
        site_logo: '',
        site_description: '专业的EDU邮箱销售平台',
        site_keywords: 'EDU邮箱,教育邮箱,学生邮箱',
        contact_email: '<EMAIL>',
        customer_service_qq: '',
        customer_service_wechat: ''
      },
      payment: {
        payment_alipay_enabled: 'false',
        payment_wechat_enabled: 'false',
        payment_mock_enabled: 'true'
      },
      email: {
        email_smtp_host: '',
        email_smtp_port: '587',
        email_smtp_user: '',
        email_smtp_password: '',
        email_from_name: 'EDU邮箱平台'
      }
    };

    const settingsToReset = defaultSettings[category];

    // 开始事务重置配置
    await db.execute('START TRANSACTION');

    try {
      for (const [key, value] of Object.entries(settingsToReset)) {
        await db.execute(
          'UPDATE system_settings SET `value` = ?, updated_at = CURRENT_TIMESTAMP WHERE `key` = ?',
          [value, key]
        );
      }

      await db.execute('COMMIT');

      success(res, { 
        category: category,
        reset_keys: Object.keys(settingsToReset)
      }, `${category}配置重置成功`);

    } catch (err) {
      await db.execute('ROLLBACK');
      throw err;
    }

  } catch (err) {
    console.error('重置配置错误:', err);
    error(res, '重置配置失败', 500);
  }
}

module.exports = {
  getAllSettings,
  getSiteSettings,
  updateSiteSettings,
  getPaymentSettings,
  updatePaymentSettings,
  getEmailSettings,
  updateEmailSettings,
  testEmailSettings,
  resetSettings
};
