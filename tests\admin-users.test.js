const request = require('supertest');
const express = require('express');
const cors = require('cors');
const { setupTestDatabase, cleanupTestDatabase } = require('./setup');

// 创建测试应用
const app = express();
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 导入路由
const adminRoutes = require('../src/routes/admin');
app.use('/api/admin', adminRoutes);

describe('管理员用户管理 API 测试', () => {
  let adminToken = '';
  let testUserId = '';

  beforeAll(async () => {
    await setupTestDatabase();
    
    // 获取管理员token
    const loginResponse = await request(app)
      .post('/api/admin/auth/login')
      .send({
        username: 'testadmin',
        password: 'admin123'
      });
    
    adminToken = loginResponse.body.data.token;
  });

  afterAll(async () => {
    await cleanupTestDatabase();
  });

  describe('GET /api/admin/users', () => {
    test('应该能够获取用户列表', async () => {
      const response = await request(app)
        .get('/api/admin/users')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('users');
      expect(response.body.data).toHaveProperty('pagination');
      expect(Array.isArray(response.body.data.users)).toBe(true);

      // 验证分页信息
      expect(response.body.data.pagination).toHaveProperty('current_page');
      expect(response.body.data.pagination).toHaveProperty('per_page');
      expect(response.body.data.pagination).toHaveProperty('total');
      expect(response.body.data.pagination).toHaveProperty('total_pages');

      // 如果有用户数据，验证数据结构
      if (response.body.data.users.length > 0) {
        const firstUser = response.body.data.users[0];
        expect(firstUser).toHaveProperty('id');
        expect(firstUser).toHaveProperty('username');
        expect(firstUser).toHaveProperty('email');
        expect(firstUser).toHaveProperty('role');
        expect(firstUser).toHaveProperty('order_count');
        expect(firstUser).toHaveProperty('total_spent');
        expect(firstUser).toHaveProperty('created_at');
      }
    });

    test('应该支持搜索功能', async () => {
      const response = await request(app)
        .get('/api/admin/users')
        .query({ search: 'testuser' })
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      
      // 搜索结果应该包含搜索关键词
      if (response.body.data.users.length > 0) {
        const hasMatchingUser = response.body.data.users.some(user => 
          user.username.includes('testuser') || user.email.includes('testuser')
        );
        expect(hasMatchingUser).toBe(true);
      }
    });

    test('应该支持角色筛选', async () => {
      const response = await request(app)
        .get('/api/admin/users')
        .query({ role: 'customer' })
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      
      // 所有返回的用户都应该是customer角色
      response.body.data.users.forEach(user => {
        expect(user.role).toBe('customer');
      });
    });

    test('应该支持分页', async () => {
      const response = await request(app)
        .get('/api/admin/users')
        .query({ page: 1, limit: 5 })
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.pagination.per_page).toBe(5);
      expect(response.body.data.users.length).toBeLessThanOrEqual(5);
    });

    test('应该拒绝未认证的请求', async () => {
      const response = await request(app)
        .get('/api/admin/users');

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });
  });

  describe('POST /api/admin/users', () => {
    test('应该能够创建新用户', async () => {
      const newUser = {
        username: `testuser_${Date.now()}`,
        email: `testuser_${Date.now()}@example.com`,
        password: 'password123',
        role: 'customer'
      };

      const response = await request(app)
        .post('/api/admin/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(newUser);

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data.user.username).toBe(newUser.username);
      expect(response.body.data.user.email).toBe(newUser.email);
      expect(response.body.data.user.role).toBe(newUser.role);
      expect(response.body.data.user).not.toHaveProperty('password_hash');

      // 保存用户ID用于后续测试
      testUserId = response.body.data.user.id;
    });

    test('应该拒绝重复的用户名', async () => {
      const duplicateUser = {
        username: 'newtestuser',
        email: '<EMAIL>',
        password: 'password123',
        role: 'customer'
      };

      const response = await request(app)
        .post('/api/admin/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(duplicateUser);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('已存在');
    });

    test('应该拒绝重复的邮箱', async () => {
      const duplicateUser = {
        username: 'anothertestuser',
        email: '<EMAIL>',
        password: 'password123',
        role: 'customer'
      };

      const response = await request(app)
        .post('/api/admin/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(duplicateUser);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('已存在');
    });

    test('应该验证必填字段', async () => {
      const incompleteUser = {
        username: 'incomplete'
        // 缺少email和password
      };

      const response = await request(app)
        .post('/api/admin/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(incompleteUser);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/admin/users/:id', () => {
    test('应该能够获取用户详情', async () => {
      const response = await request(app)
        .get(`/api/admin/users/${testUserId}`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data.user.id).toBe(testUserId);
      expect(response.body.data.user).toHaveProperty('statistics');
      expect(response.body.data.user).toHaveProperty('recent_orders');

      // 验证统计信息结构
      expect(response.body.data.user.statistics).toHaveProperty('total_orders');
      expect(response.body.data.user.statistics).toHaveProperty('completed_orders');
      expect(response.body.data.user.statistics).toHaveProperty('total_spent');
      expect(response.body.data.user.statistics).toHaveProperty('cart_items');
    });

    test('应该返回404对于不存在的用户', async () => {
      const response = await request(app)
        .get('/api/admin/users/99999')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
    });
  });

  describe('PUT /api/admin/users/:id', () => {
    test('应该能够更新用户信息', async () => {
      const updateData = {
        username: 'updatedtestuser',
        email: '<EMAIL>'
      };

      const response = await request(app)
        .put(`/api/admin/users/${testUserId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updateData);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.user.username).toBe(updateData.username);
      expect(response.body.data.user.email).toBe(updateData.email);
    });

    test('应该能够更新用户角色', async () => {
      const updateData = {
        role: 'admin'
      };

      const response = await request(app)
        .put(`/api/admin/users/${testUserId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updateData);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.user.role).toBe('admin');
    });

    test('应该返回404对于不存在的用户', async () => {
      const response = await request(app)
        .put('/api/admin/users/99999')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({ username: 'test' });

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
    });
  });

  describe('PUT /api/admin/users/:id/password', () => {
    test('应该能够重置用户密码', async () => {
      const response = await request(app)
        .put(`/api/admin/users/${testUserId}/password`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({ new_password: 'newpassword123' });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    test('应该验证密码长度', async () => {
      const response = await request(app)
        .put(`/api/admin/users/${testUserId}/password`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({ new_password: '123' });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('密码长度');
    });
  });

  describe('DELETE /api/admin/users/:id', () => {
    test('应该能够删除用户', async () => {
      const response = await request(app)
        .delete(`/api/admin/users/${testUserId}`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);

      // 验证用户已被删除
      const getResponse = await request(app)
        .get(`/api/admin/users/${testUserId}`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(getResponse.status).toBe(404);
    });

    test('应该返回404对于不存在的用户', async () => {
      const response = await request(app)
        .delete('/api/admin/users/99999')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
    });
  });
});
